<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            color: #333;
        }
        .test-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            line-height: 1.5;
        }
        .test-button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #722ED1 0%, #531DAB 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        .test-button:hover {
            opacity: 0.9;
        }
        .user-info {
            background: #e6f7ff;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .avatar-preview {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin: 10px auto;
            display: block;
            border: 2px solid #722ED1;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">AI助手修改测试</h1>
        
        <div class="test-info">
            <strong>修改内容：</strong><br>
            1. ✅ 大幅减少对话间距，使对话更紧凑<br>
            2. ✅ 修复用户头像显示问题<br>
            3. ✅ 修复 uni.onShow 和 uni.onStorageChange 兼容性问题<br>
            4. ✅ 优化消息气泡内边距和头像间距
        </div>
        
        <button class="test-button" onclick="setTestUserInfo()">
            设置测试用户信息
        </button>
        
        <button class="test-button" onclick="clearUserInfo()">
            清除用户信息
        </button>
        
        <button class="test-button" onclick="showCurrentUserInfo()">
            查看当前用户信息
        </button>
        
        <div id="userInfoDisplay" class="user-info" style="display: none;">
            <h3>当前用户信息：</h3>
            <div id="userInfoContent"></div>
        </div>
    </div>

    <script>
        // 模拟 uni 对象的基本功能
        if (typeof uni === 'undefined') {
            window.uni = {
                getStorageSync: function(key) {
                    return JSON.parse(localStorage.getItem(key) || 'null');
                },
                setStorageSync: function(key, value) {
                    localStorage.setItem(key, JSON.stringify(value));
                },
                onShow: function(callback) {
                    // H5环境下可以监听页面可见性变化
                    document.addEventListener('visibilitychange', function() {
                        if (!document.hidden) {
                            callback();
                        }
                    });
                }
            };
        }

        function setTestUserInfo() {
            const testUser = {
                userId: 'test123',
                nickname: '测试用户',
                avatar: 'https://fcg02.oss-cn-guangzhou.aliyuncs.com/image/avatar/default.png',
                phone: '138****8888',
                memberLevel: 1
            };
            
            uni.setStorageSync('userInfo', testUser);
            alert('测试用户信息已设置！\n现在可以打开AI助手查看头像显示效果。');
            showCurrentUserInfo();
        }

        function clearUserInfo() {
            localStorage.removeItem('userInfo');
            alert('用户信息已清除！');
            document.getElementById('userInfoDisplay').style.display = 'none';
        }

        function showCurrentUserInfo() {
            const userInfo = uni.getStorageSync('userInfo');
            const display = document.getElementById('userInfoDisplay');
            const content = document.getElementById('userInfoContent');
            
            if (userInfo) {
                content.innerHTML = `
                    <p><strong>昵称：</strong>${userInfo.nickname}</p>
                    <p><strong>手机：</strong>${userInfo.phone}</p>
                    <p><strong>会员等级：</strong>${userInfo.memberLevel}</p>
                    <p><strong>头像：</strong></p>
                    <img src="${userInfo.avatar}" class="avatar-preview" alt="用户头像">
                `;
                display.style.display = 'block';
            } else {
                content.innerHTML = '<p>暂无用户信息</p>';
                display.style.display = 'block';
            }
        }

        // 页面加载时显示当前用户信息
        window.onload = function() {
            showCurrentUserInfo();
        };
    </script>
</body>
</html>
