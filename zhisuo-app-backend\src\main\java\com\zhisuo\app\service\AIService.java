package com.zhisuo.app.service;

import com.zhisuo.app.dto.request.AIAnalysisRequest;
import com.zhisuo.app.dto.request.AIChatRequest;
import com.zhisuo.app.dto.response.AIAnalysisResponse;
import com.zhisuo.app.dto.response.AIChatResponse;

/**
 * AI服务接口
 */
public interface AIService {
    
    /**
     * AI对话
     * @param request 对话请求
     * @return 对话响应
     */
    AIChatResponse chat(AIChatRequest request);
    
    /**
     * AI内容分析
     * @param request 分析请求
     * @return 分析响应
     */
    AIAnalysisResponse analyze(AIAnalysisRequest request);
}
