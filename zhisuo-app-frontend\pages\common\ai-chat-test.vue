<template>
	<view class="test-page">
		<view class="test-header">
			<view class="back-btn" @click="goBack">
				<uni-icons type="left" size="20" color="#333"></uni-icons>
				<text>返回</text>
			</view>
			<view class="test-title">AI对话测试</view>
		</view>
		
		<view class="test-content">
			<view class="test-section">
				<view class="section-title">功能测试</view>
				<view class="test-card">
					<view class="card-title">🤖 AI对话功能</view>
					<view class="card-desc">测试与硅基流动API的实时对话功能</view>
					<view class="test-status" :class="{ 'success': apiStatus === 'success', 'error': apiStatus === 'error' }">
						状态: {{ apiStatusText }}
					</view>
				</view>
				
				<view class="test-card">
					<view class="card-title">📊 智能分析功能</view>
					<view class="card-desc">测试AI生成的内容分析报告</view>
					<view class="test-controls">
						<view class="control-btn" @click="testAnalysis">
							<text>测试分析功能</text>
						</view>
					</view>
				</view>
			</view>
			
			<view class="test-section">
				<view class="section-title">测试数据</view>
				<view class="config-info">
					<view class="config-item">
						<text class="config-label">页面类型:</text>
						<text class="config-value">{{ currentPageType }}</text>
					</view>
					<view class="config-item">
						<text class="config-label">API状态:</text>
						<text class="config-value">{{ apiStatusText }}</text>
					</view>
					<view class="config-item">
						<text class="config-label">测试内容:</text>
						<text class="config-value">{{ testData.title }}</text>
					</view>
				</view>
			</view>
			
			<view class="test-section">
				<view class="section-title">快速测试</view>
				<view class="quick-tests">
					<view class="quick-test-btn" @click="sendTestMessage('你好，请介绍一下你自己')">
						<text>问候测试</text>
					</view>
					<view class="quick-test-btn" @click="sendTestMessage('请分析一下当前的内容')">
						<text>分析测试</text>
					</view>
					<view class="quick-test-btn" @click="sendTestMessage('你有什么功能？')">
						<text>功能测试</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- AI小助手 -->
		<AIAssistant 
			:pageType="currentPageType" 
			:contentData="testData" 
			:visible="true"
			:showAnalysis="true"
		/>
	</view>
</template>

<script>
import AIAssistant from '../../components/AIAssistant.vue';
import { callAIChat } from '../../common/api/aiService.js';

export default {
	components: {
		AIAssistant
	},
	data() {
		return {
			currentPageType: 'article',
			apiStatus: 'unknown', // unknown, success, error
			testData: {
				title: 'AI技术发展趋势分析报告',
				description: '深入探讨人工智能技术在各个领域的应用现状和未来发展趋势，包括机器学习、深度学习、自然语言处理等核心技术的突破与创新。',
				author: '智索研究院',
				publishTime: new Date().toISOString(),
				viewCount: 3500,
				likeCount: 280,
				commentCount: 45
			}
		}
	},
	computed: {
		apiStatusText() {
			switch (this.apiStatus) {
				case 'success':
					return '连接正常';
				case 'error':
					return '连接失败';
				default:
					return '未测试';
			}
		}
	},
	mounted() {
		this.testAPIConnection();
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		
		async testAPIConnection() {
			try {
				const testMessage = [{
					role: 'user',
					content: '你好，这是一个连接测试'
				}];
				
				await callAIChat(testMessage);
				this.apiStatus = 'success';
				
				uni.showToast({
					title: 'API连接成功',
					icon: 'success'
				});
			} catch (error) {
				console.error('API连接测试失败:', error);
				this.apiStatus = 'error';
				
				uni.showToast({
					title: 'API连接失败',
					icon: 'error'
				});
			}
		},
		
		testAnalysis() {
			// 触发AI助手的分析功能
			uni.showToast({
				title: '请在AI助手中切换到分析标签',
				icon: 'none'
			});
		},
		
		sendTestMessage(message) {
			// 这里可以通过事件或其他方式向AI助手发送测试消息
			uni.showToast({
				title: `测试消息: ${message}`,
				icon: 'none'
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.test-page {
	min-height: 100vh;
	background-color: #f8f9fc;
}

.test-header {
	display: flex;
	align-items: center;
	padding: 20rpx 30rpx;
	background-color: #fff;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	
	.back-btn {
		display: flex;
		align-items: center;
		margin-right: 30rpx;
		
		text {
			margin-left: 10rpx;
			font-size: 28rpx;
			color: #333;
		}
	}
	
	.test-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
}

.test-content {
	padding: 30rpx;
}

.test-section {
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	padding-left: 20rpx;
	border-left: 6rpx solid #722ED1;
}

.test-card {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	
	.card-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 16rpx;
	}
	
	.card-desc {
		font-size: 26rpx;
		color: #666;
		line-height: 1.6;
		margin-bottom: 16rpx;
	}
}

.test-status {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	display: inline-block;
	
	&.success {
		background-color: #f6ffed;
		color: #52c41a;
		border: 1rpx solid #b7eb8f;
	}
	
	&.error {
		background-color: #fff2f0;
		color: #ff4d4f;
		border: 1rpx solid #ffccc7;
	}
}

.test-controls {
	margin-top: 20rpx;
}

.control-btn {
	background-color: #722ED1;
	color: #fff;
	padding: 20rpx 30rpx;
	border-radius: 12rpx;
	text-align: center;
	font-size: 26rpx;
	font-weight: 500;
	transition: all 0.3s ease;
	
	&:active {
		background-color: #5a1ea6;
		transform: scale(0.98);
	}
}

.config-info {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.config-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
}

.config-label {
	font-size: 26rpx;
	color: #666;
}

.config-value {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}

.quick-tests {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.quick-test-btn {
	background-color: #fff;
	color: #722ED1;
	padding: 24rpx;
	border-radius: 12rpx;
	text-align: center;
	font-size: 26rpx;
	font-weight: 500;
	border: 2rpx solid #722ED1;
	transition: all 0.3s ease;
	
	&:active {
		background-color: #722ED1;
		color: #fff;
		transform: scale(0.98);
	}
}
</style>
