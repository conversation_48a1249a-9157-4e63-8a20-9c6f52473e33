# AIAssistant 组件使用说明

## 概述
AIAssistant 是一个智能助手组件，支持两种模式：
1. **完整模式**：包含聊天和分析功能（适用于文章详情等页面）
2. **聊天模式**：仅包含聊天功能（适用于首页、发现、我的等页面）

## 使用方法

### 1. 导入组件
```vue
<script>
import AIAssistant from '@/components/AIAssistant.vue';

export default {
  components: {
    AIAssistant
  },
  // ...
}
</script>
```

### 2. 在模板中使用

#### 完整模式（聊天 + 分析）
```vue
<template>
  <view class="page">
    <!-- 页面内容 -->
    
    <!-- AI助手 - 完整功能 -->
    <AIAssistant 
      :page-type="'article'" 
      :content-data="articleData" 
    />
  </view>
</template>
```

#### 聊天模式（仅聊天）
```vue
<template>
  <view class="page">
    <!-- 页面内容 -->

    <!-- AI助手 - 仅聊天功能 -->
    <AIAssistant :show-analysis="false" />
  </view>
</template>
```



## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| pageType | String | 'article' | 页面类型，用于确定分析内容 |
| contentData | Object | {} | 内容数据，用于分析功能 |
| visible | Boolean | true | 是否显示助手 |
| showAnalysis | Boolean | true | 是否显示分析功能 |

## 当前集成页面

### 已集成的页面：
- ✅ **首页** (`pages/index/index.vue`) - 仅聊天模式
- ✅ **发现页** (`pages/discover/discover.vue`) - 仅聊天模式  
- ✅ **我的页面** (`pages/mine/mine.vue`) - 仅聊天模式
- ✅ **文章详情页** (`pages/article/detail.vue`) - 完整模式

### 使用示例：

#### 首页、发现、我的页面
```vue
<!-- 仅聊天功能 -->
<AIAssistant :show-analysis="false" />
```

#### 文章详情页
```vue
<!-- 完整功能（聊天 + 分析） -->
<AIAssistant 
  :page-type="'article'" 
  :content-data="articleData" 
/>
```

## 功能特性

### 聊天功能
- 浮窗按钮显示/隐藏
- 实时聊天对话
- 打字动画效果
- 消息历史记录
- 自动滚动到底部

### 拖拽功能 🆕
- **长按拖拽**：长按500ms后进入拖拽模式，可拖动浮窗按钮到任意位置
- **点击打开**：轻触浮窗按钮直接打开AI助手面板
- **位置记忆**：自动保存按钮位置，下次打开应用时恢复
- **边界限制**：防止按钮拖拽到屏幕外
- **视觉反馈**：长按和拖拽时按钮会有不同的视觉效果

### 分析功能（仅完整模式）
- 内容摘要分析
- 核心观点提取
- 深度洞察分析
- 相关建议推荐
- 数据统计展示

## 样式特性
- **主题色**：紫色系 (#722ED1, #B37FEB)
- **位置**：可拖拽浮窗（默认右下角）
- **响应式**：自适应不同屏幕尺寸
- **动画**：平滑的显示/隐藏过渡效果
- **拖拽反馈**：拖拽时的视觉效果和状态变化

## 注意事项
1. 确保项目中已安装 uni-icons 组件
2. 确保 `/static/logo.png` 文件存在
3. 组件使用固定定位，不会影响页面布局
4. 建议在页面模板的最后添加此组件
5. 聊天模式下不会显示标签页，直接显示聊天界面
6. 拖拽功能会自动保存位置到本地存储
7. 长按500ms后进入拖拽模式，避免误触
8. 提供了丰富的视觉动画反馈

## 故障排除

### 长按拖拽问题修复 🔧
已修复第一次长按无法拖动的问题，现在使用双重检测机制：

#### 修复内容
1. **时间检测**：触摸时间超过500ms自动允许拖拽
2. **状态检测**：长按状态激活后允许拖拽
3. **容忍度提升**：移动50px以内不会取消长按

#### 测试步骤
1. **点击测试**：轻触浮窗按钮（<500ms），应该直接打开AI助手
2. **长按测试**：长按浮窗按钮500ms，按钮应该变色放大
3. **拖拽测试**：长按后移动手指，浮窗应该立即跟随移动
4. **第一次测试**：特别测试第一次长按是否能正常拖动
5. **位置保存**：拖拽到新位置后松开，重新打开应用位置应该保持

#### 预期行为
- ✅ 第一次长按就能正常拖动
- ✅ 长按500ms后立即响应拖拽
- ✅ 轻微手抖不会影响长按检测
- ✅ 拖拽过程流畅跟手

## 扩展建议
- 可以集成真实的AI API服务
- 可以添加语音输入功能
- 可以添加消息持久化存储
- 可以根据不同页面定制不同的AI回复策略
- 可以添加磁性吸附功能（拖拽到边缘时自动吸附）
- 可以添加双击重置位置功能
- 可以添加更多拖拽手势和动画效果

## 拖拽功能详细说明

### 使用方法
1. **点击打开**：轻触浮窗按钮（<500ms）直接打开AI助手面板
2. **长按拖拽**：长按浮窗按钮500ms后进入拖拽模式，然后拖动到想要的位置
3. **视觉提示**：长按500ms后按钮会有视觉动画，提示进入拖拽模式
4. **位置保存**：松开手指后位置会自动保存

### 技术实现
- 使用 `touchstart`、`touchmove`、`touchend` 事件
- 通过移动距离阈值区分拖拽和点击
- 实时计算边界限制，防止按钮移出屏幕
- 使用 `uni.getStorageSync` 和 `uni.setStorageSync` 持久化位置
- 动态更新 CSS 样式实现位置变化
- 真实跟随手指移动，提供自然的拖拽体验

### 交互机制优化

#### 核心改进
- **长按触发**：500ms长按后进入拖拽模式，避免误触
- **点击直达**：短触摸直接打开AI助手，提高效率
- **真实跟随**：拖拽时浮窗完全跟随手指移动轨迹
- **视觉反馈**：长按和拖拽有不同的视觉状态

#### 状态机制
```javascript
// 触摸开始：启动长按定时器
setTimeout(() => {
  this.isLongPressing = true; // 进入长按状态
}, 500);

// 触摸结束：判断操作类型
if (this.isDragging) {
  // 完成拖拽
  this.finishDrag();
} else if (touchDuration < 500) {
  // 触发点击
  this.handleClick();
}
```

#### 视觉状态
- **正常状态**：紫色渐变 + 脉冲动画
- **长按状态**：轻微放大 + 颜色变化
- **拖拽状态**：明显放大 + 增强阴影
