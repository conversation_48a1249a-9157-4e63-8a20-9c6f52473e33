<template>
	<view class="page">
		<!-- 顶部背景与用户信息区 -->
		<view class="top-section">
			<view class="blur-bg"></view>

			<!-- 顶部导航栏 -->
			<view class="navbar">
				<view class="title">个人中心</view>
			</view>

			<!-- 用户信息 -->
			<view class="user-info">
				<view class="avatar-container">
					<view class="avatar">
						<image :src="userInfo?.avatar || '/static/logo.png'" mode="aspectFill"></image>
					</view>
					<view class="vip-badge" v-if="userInfo?.memberLevel > 0">
						<text>{{ getMemberLevelText }}</text>
					</view>
				</view>
				<view class="user-details">
					<view class="username">{{ userInfo?.nickname || '未登录' }}</view>
					<view class="profile-link" @click="navigateToProfile">
						查看个人主页 <uni-icons type="right" size="14" color="#fff"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 数据概览 -->
			<view class="stats-overview">
				<view class="stat-item">
					<text class="stat-value">{{ userStats.favoriteCount || 0 }}</text>
					<text class="stat-label">收藏</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-value">{{ userStats.analysisCount || 0 }}</text>
					<text class="stat-label">分析</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-value">{{ userStats.followCount || 0 }}</text>
					<text class="stat-label">关注</text>
				</view>
			</view>
		</view>

		<!-- 功能模块容器 -->
		<view class="modules-container">
			<!-- 兴趣分布卡片 -->
			<view class="module-card">
				<view class="card-header">
					<text class="card-title">兴趣分布</text>
					<view class="header-right">
						<uni-icons type="reload" size="18" color="#999"></uni-icons>
						<text class="update-text">刚刚更新</text>
					</view>
				</view>

				<view class="interest-chart">
					<view class="chart-container">
						<view class="ring-chart" v-if="getDisplayInterests().length > 0">
							<!-- 动态环形图 -->
							<view
								v-for="(item, index) in getDisplayInterests()"
								:key="index"
								class="ring-segment"
								:style="getRingSegmentStyle(item, index)"
							></view>
							<view class="ring-inner">
								<text class="ring-center-text">兴趣</text>
							</view>
						</view>
						<view class="ring-chart" v-else>
							<!-- 空状态显示 -->
							<view class="ring-empty">
								<text class="empty-text">暂无数据</text>
							</view>
						</view>
					</view>

					<view class="chart-legend">
						<view class="legend-item" v-for="(item, index) in getDisplayInterests()" :key="index">
							<view class="legend-color" :style="{ background: item.color }"></view>
							<view class="legend-text">{{ item.name }} ({{ item.percentage }}%)</view>
						</view>
						<view class="legend-item" v-if="!userInterests.data || userInterests.data.length === 0">
							<view class="empty-state">
								<text>暂无兴趣分布数据</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 我的收藏卡片 -->
			<view class="module-card">
				<view class="card-header">
					<text class="card-title">我的收藏</text>
					<view class="more" @click="navigateToFavorites">
						查看更多 <uni-icons type="right" size="14" color="#3264ED"></uni-icons>
					</view>
				</view>

				<view class="collection-list">
					<view class="collection-item" v-for="(item, index) in myFavorites" :key="index" @click="navigateToContent(item)">
						<view class="collection-image">
							<image :src="item.contentImage || '/static/images/pageBg.png'" mode="aspectFill"></image>
							<view class="collection-overlay">
								<uni-icons type="eye-filled" size="16" color="#fff"></uni-icons>
								<text>{{ item.viewCount || 0 }}</text>
							</view>
						</view>
						<view class="collection-title">{{ item.contentTitle || item.title }}</view>
						<view class="collection-info">
							<view class="collection-tag">{{ getContentTypeLabel(item) }}</view>
							<view class="collection-time">{{ formatTime(item.createTime) }}</view>
						</view>
					</view>
					<view class="collection-item" v-if="myFavorites.length === 0">
						<view class="empty-state">
							<text>暂无收藏内容</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 最近分析卡片 -->
			<view class="module-card">
				<view class="card-header">
					<text class="card-title">最近分析</text>
				</view>

				<view class="analysis-list">
					<view class="analysis-item" v-for="(item, index) in userAnalysis" :key="index">
						<view class="analysis-left">
							<view class="analysis-date">
								<text class="date-day">{{ formatDay(item.createTime) }}</text>
								<text class="date-month">{{ formatMonth(item.createTime) }}</text>
							</view>
						</view>
						<view class="analysis-content">
							<view class="analysis-title">{{ item.title }}</view>
							<view class="analysis-desc">{{ item.description }}</view>
							<view class="analysis-meta">
								<view class="meta-item">
									<uni-icons type="fire-filled" size="14" color="#ff7d00"></uni-icons>
									<text>热度 {{ item.hotValue }}</text>
								</view>
								<view class="meta-item">
									<uni-icons type="chat" size="14" color="#3264ED"></uni-icons>
									<text>评论 {{ item.commentCount }}</text>
								</view>
							</view>
						</view>
					</view>
					<view class="analysis-item" v-if="userAnalysis.length === 0">
						<view class="empty-state">
							<text>暂无分析记录</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 功能菜单卡片 -->
			<view class="module-card">
				<view class="card-header">
					<text class="card-title">常用功能</text>
				</view>

				<!-- 主要功能菜单（始终显示） -->
				<view class="function-menu">
					<view class="function-item">
						<view class="function-icon notify">
							<uni-icons type="notification-filled" size="24" color="#3264ED"></uni-icons>
						</view>
						<view class="function-name">通知提醒</view>
					</view>
					<view class="function-item">
						<view class="function-icon privacy">
							<uni-icons type="eye-filled" size="24" color="#3264ED"></uni-icons>
						</view>
						<view class="function-name">隐私设置</view>
					</view>
					<view class="function-item">
						<view class="function-icon security">
							<uni-icons type="locked-filled" size="24" color="#3264ED"></uni-icons>
						</view>
						<view class="function-name">账号安全</view>
					</view>
					<view class="function-item" @click="toggleMoreSettings">
						<view class="function-icon more" :class="{'active': showMoreSettings}">
							<uni-icons :type="showMoreSettings ? 'top' : 'more-filled'" size="24"
								color="#3264ED"></uni-icons>
						</view>
						<view class="function-name">{{ showMoreSettings ? '收起' : '更多设置' }}</view>
					</view>
				</view>

				<!-- 更多设置菜单（下拉显示） -->
				<view class="more-settings" v-if="showMoreSettings">
					<view class="function-menu">
						<view class="function-item">
							<view class="function-icon">
								<uni-icons type="gear" size="24" color="#3264ED"></uni-icons>
							</view>
							<view class="function-name">系统设置</view>
						</view>
						<view class="function-item">
							<view class="function-icon">
								<uni-icons type="chatboxes" size="24" color="#3264ED"></uni-icons>
							</view>
							<view class="function-name">意见反馈</view>
						</view>
						<view class="function-item">
							<view class="function-icon">
								<uni-icons type="refresh" size="24" color="#3264ED"></uni-icons>
							</view>
							<view class="function-name">清除缓存</view>
						</view>
						<view class="function-item">
							<view class="function-icon">
								<uni-icons type="help" size="24" color="#3264ED"></uni-icons>
							</view>
							<view class="function-name">帮助中心</view>
						</view>
						<view class="function-item">
							<view class="function-icon">
								<uni-icons type="info" size="24" color="#3264ED"></uni-icons>
							</view>
							<view class="function-name">关于我们</view>
						</view>
					</view>
				</view>

				<!-- 退出登录按钮 -->
				<view class="logout-button" @click="logout">
					退出登录
				</view>
			</view>
		</view>
	</view>

	<!-- AI智能助手 -->
	<AIAssistant :show-analysis="false" />
</template>

<script>
	import Api from '../../common/api.js';
	import AIAssistant from '../../components/AIAssistant.vue';

	export default {
		components: {
			AIAssistant
		},
		data() {
			return {
				showMoreSettings: false, // 控制更多设置菜单的显示状态
				userInfo: null, // 添加 userInfo 数据属性
				userStats: {}, // 用户统计数据
				userInterests: { data: [] }, // 用户兴趣分布
				myFavorites: [], // 我的收藏
				userAnalysis: [] // 用户分析
			}
		},
		onLoad() {
			// 检查登录状态并获取用户信息
			this.loadUserInfo();
			this.loadUserStats();
			this.loadUserInterests();
			this.loadMyFavorites();
			this.loadUserAnalysis();
		},
		onShow() {
			// 每次页面显示时重新加载用户信息，确保信息最新
			this.loadUserInfo();
			this.loadUserStats();
			this.loadMyFavorites();
		},
		computed: {
			// 获取会员等级文本
			getMemberLevelText() {
				const level = this.userInfo?.memberLevel || 0;
				switch (level) {
					case 1:
						return '黄金会员';
					case 2:
						return '铂金会员';
					case 3:
						return '钻石会员';
					default:
						return '普通会员';
				}
			}
		},
		methods: {
			// 加载用户信息
			loadUserInfo() {
				const token = uni.getStorageSync('token');
				if (!token) {
					// 未登录，跳转到登录页
					uni.redirectTo({
						url: '/pages/login/login'
					});
					return;
				}

				// 从本地存储获取用户信息
				this.userInfo = uni.getStorageSync('userInfo');
				//console.log('个人中心用户信息:', this.userInfo);

				// 可以在这里添加从服务器刷新用户信息的逻辑
				this.refreshUserInfo();
			},

			// 刷新用户信息（可选）
			refreshUserInfo() {
				//这里可以调用API获取最新用户信息
				Api.request({
				    url: '/v1/user/info',
				    method: 'GET'
				}).then(res => {
				    if (res.data && res.data.code === 0) {
				        this.userInfo = res.data.data;
				        uni.setStorageSync('userInfo', this.userInfo);
				    }
				}).catch(err => {
				    console.error('获取用户信息失败:', err);
				});
			},

			// 跳转到个人主页
			navigateToProfile() {
				// 个人主页跳转逻辑
				uni.navigateTo({
					url: '/pages/mine/profile'
				});
			},

			// 跳转到收藏页面
			navigateToFavorites() {
				uni.navigateTo({
					url: '/pages/mine/favorites'
				});
			},

			// 跳转到内容详情页面
			navigateToContent(item) {
				if (!item || !item.contentId || !item.contentType) {
					console.error('内容信息不完整:', item);
					return;
				}

				let url = '';
				if (item.contentType === 'article') {
					url = `/pages/article/detail?id=${item.contentId}`;
				} else if (item.contentType === 'topic') {
					url = `/pages/hot/detail?id=${item.contentId}`;
				} else {
					console.error('未知的内容类型:', item.contentType);
					return;
				}

				uni.navigateTo({
					url: url
				});
			},

			// 获取内容类型标签
			getContentTypeLabel(item) {
				if (!item) return '';

				// 检查多个可能的字段名
				const contentType = item.contentType || item.content_type;

				console.log('内容类型判断:', {
					contentType: contentType,
					item: item
				});

				if (contentType === 'article') {
					return '文章';
				} else if (contentType === 'topic') {
					return '话题';
				} else {
					return contentType || '未知';
				}
			},

			// 切换显示更多设置菜单
			toggleMoreSettings() {
				this.showMoreSettings = !this.showMoreSettings;
			},

			// 登出功能
			logout() {
				uni.showModal({
					title: '确认退出',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							// 清除本地存储的token和用户信息
							uni.removeStorageSync('token');
							uni.removeStorageSync('refreshToken');
							uni.removeStorageSync('userInfo');

							// 跳转到登录页
							uni.reLaunch({
								url: '/pages/login/login'
							});
						}
					}
				});
			},

			// 加载用户统计数据
			loadUserStats() {
				Api.request({
					url: '/v1/user/stats',
					method: 'GET'
				}).then(res => {
					if (res.data && res.data.code === 0) {
						this.userStats = res.data.data;
					}
				}).catch(err => {
					console.error('获取用户统计数据失败:', err);
				});
			},

			// 加载用户兴趣分布
			loadUserInterests() {
				Api.request({
					url: '/v1/user/interests',
					method: 'GET'
				}).then(res => {
					if (res.data && res.data.code === 0) {
						this.userInterests = res.data.data;
					}
				}).catch(err => {
					console.error('获取用户兴趣分布失败:', err);
				});
			},

			// 加载我的收藏
			loadMyFavorites() {
				Api.request({
					url: '/v1/favorites/my',
					method: 'GET',
					data: {
						page: 0,
						size: 2 // 只显示前2个
					}
				}).then(res => {
					if (res.data && res.data.code === 0) {
						this.myFavorites = res.data.data.content || [];
					}
				}).catch(err => {
					console.error('获取我的收藏失败:', err);
				});
			},

			// 加载用户分析
			loadUserAnalysis() {
				Api.request({
					url: '/v1/user/analysis',
					method: 'GET',
					data: {
						page: 0,
						size: 3 // 只显示前3个
					}
				}).then(res => {
					if (res.data && res.data.code === 0) {
						this.userAnalysis = res.data.data.content || [];
					}
				}).catch(err => {
					console.error('获取用户分析失败:', err);
				});
			},

			// 格式化时间
			formatTime(time) {
				if (!time) return '';
				const date = new Date(time);
				const now = new Date();
				const diff = now - date;

				if (diff < 60 * 60 * 1000) {
					return Math.floor(diff / (60 * 1000)) + '分钟前';
				} else if (diff < 24 * 60 * 60 * 1000) {
					return Math.floor(diff / (60 * 60 * 1000)) + '小时前';
				} else if (diff < 7 * 24 * 60 * 60 * 1000) {
					return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前';
				} else {
					return date.getMonth() + 1 + '月' + date.getDate() + '日';
				}
			},

			// 格式化日期 - 日
			formatDay(time) {
				if (!time) return '';
				const date = new Date(time);
				return date.getDate();
			},

			// 格式化日期 - 月
			formatMonth(time) {
				if (!time) return '';
				const date = new Date(time);
				return (date.getMonth() + 1) + '月';
			},

			// 获取要显示的兴趣数据（只显示有百分比的）
			getDisplayInterests() {
				if (!this.userInterests.data) return [];
				return this.userInterests.data.filter(item => item.percentage > 0);
			},

			// 计算环形图片段样式
			getRingSegmentStyle(item, index) {
				if (!item || !item.percentage) return { display: 'none' };

				// 获取有百分比的兴趣数据
				const displayInterests = this.getDisplayInterests();

				// 计算起始角度
				let startAngle = 0;
				for (let i = 0; i < index; i++) {
					if (displayInterests[i] && displayInterests[i].percentage) {
						startAngle += (displayInterests[i].percentage / 100) * 360;
					}
				}

				// 计算当前片段的角度
				const segmentAngle = (item.percentage / 100) * 360;
				const endAngle = startAngle + segmentAngle;

				// 计算结束点坐标
				const endX = 50 + 50 * Math.sin((endAngle * Math.PI) / 180);
				const endY = 50 - 50 * Math.cos((endAngle * Math.PI) / 180);

				// 生成clip-path
				let clipPath;
				if (segmentAngle <= 180) {
					clipPath = `polygon(50% 50%, 50% 0%, ${endX}% ${endY}%)`;
				} else {
					// 大于180度的片段使用完整圆形
					clipPath = `polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 0%, ${endX}% ${endY}%)`;
				}

				return {
					transform: `rotate(${startAngle}deg)`,
					clipPath: clipPath,
					background: item.color || '#ccc'
				};
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		background-color: #f2f5fc;
		min-height: 100vh;
		position: relative;
	}

	/* 顶部区域样式 */
	.top-section {
		position: relative;
		background: linear-gradient(135deg, #722ED1, #B37FEB);
		padding: 40rpx 30rpx 30rpx;
		border-radius: 30rpx;
		overflow: hidden;
		box-shadow: 0 10rpx 30rpx rgba(50, 100, 237, 0.2);
		margin: 20rpx 20rpx 20rpx;

		.blur-bg {
			position: absolute;
			top: -50%;
			right: -50%;
			width: 200%;
			height: 200%;
			background: radial-gradient(circle at 70% 60%, rgba(255, 255, 255, 0.1) 0%, rgba(50, 100, 237, 0) 70%);
			z-index: 0;
		}
	}

	.navbar {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 40rpx;
		z-index: 1;

		.title {
			font-size: 40rpx;
			font-weight: bold;
			color: #fff;
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
		}
	}

	.user-info {
		position: relative;
		display: flex;
		align-items: center;
		z-index: 1;

		.avatar-container {
			position: relative;

			.avatar {
				width: 150rpx;
				height: 150rpx;
				border-radius: 50%;
				overflow: hidden;
				border: 6rpx solid rgba(255, 255, 255, 0.3);
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

				image {
					width: 100%;
					height: 100%;
				}
			}

			.vip-badge {
				position: absolute;
				bottom: 0;
				right: 0;
				background: linear-gradient(to right, #fa8c16, #faad14);
				color: #fff;
				padding: 4rpx 14rpx;
				border-radius: 20rpx;
				font-size: 20rpx;
				border: 2rpx solid #fff;
				box-shadow: 0 4rpx 8rpx rgba(250, 173, 20, 0.3);
				transform: translateY(50%);
			}
		}

		.user-details {
			flex: 1;
			margin-left: 30rpx;

			.username {
				font-size: 38rpx;
				font-weight: bold;
				color: #fff;
				margin-bottom: 10rpx;
				text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
			}

			.profile-link {
				font-size: 26rpx;
				color: rgba(255, 255, 255, 0.9);
				display: flex;
				align-items: center;
				width: fit-content;
				background: rgba(255, 255, 255, 0.2);
				padding: 8rpx 20rpx;
				border-radius: 30rpx;
				backdrop-filter: blur(10px);
				-webkit-backdrop-filter: blur(10px);
			}
		}
	}

	.stats-overview {
		position: relative;
		display: flex;
		justify-content: space-around;
		align-items: center;
		margin-top: 50rpx;
		padding: 20rpx 0;
		background: rgba(255, 255, 255, 0.15);
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		border-radius: 20rpx;
		z-index: 1;

		.stat-item {
			display: flex;
			flex-direction: column;
			align-items: center;

			.stat-value {
				font-size: 40rpx;
				font-weight: bold;
				color: #fff;
				margin-bottom: 4rpx;
				text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
			}

			.stat-label {
				font-size: 24rpx;
				color: rgba(255, 255, 255, 0.9);
			}
		}

		.stat-divider {
			width: 1px;
			height: 50rpx;
			background: rgba(255, 255, 255, 0.3);
		}
	}

	/* 功能模块容器 */
	.modules-container {
		padding: 20rpx;
		margin-top: -20rpx;
		position: relative;
		z-index: 10;

		.module-card {
			background-color: #fff;
			border-radius: 20rpx;
			margin-bottom: 30rpx;
			overflow: hidden;
			box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
			padding: 30rpx;

			// 退出登录按钮样式
			.logout-button {
				margin-top: 30rpx;
				background-color: #ff4d4f;
				color: white;
				height: 90rpx;
				line-height: 90rpx;
				text-align: center;
				border-radius: 45rpx;
				font-size: 32rpx;
				font-weight: bold;
				box-shadow: 0 4rpx 10rpx rgba(255, 77, 79, 0.3);
			}

			.card-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;

				.card-title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
					position: relative;
					padding-left: 20rpx;

					&:before {
						content: "";
						position: absolute;
						left: 0;
						top: 50%;
						transform: translateY(-50%);
						width: 8rpx;
						height: 32rpx;
						background: linear-gradient(to bottom, #4a89dc, #3264ED);
						border-radius: 4rpx;
					}
				}

				.more,
				.header-right {
					display: flex;
					align-items: center;
					font-size: 24rpx;
					color: #3264ED;
				}

				.header-right {
					color: #999;

					.update-text {
						margin-left: 6rpx;
					}
				}
			}
		}
	}

	/* 功能菜单样式 */
	.function-menu {
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;

		.function-item {
			text-align: center;
			width: 25%;
			margin-bottom: 20rpx;

			.function-icon {
				width: 100rpx;
				height: 100rpx;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 auto 16rpx;
				background: linear-gradient(135deg, #f0f6ff, #e0ecff);
				box-shadow: 0 4rpx 12rpx rgba(50, 100, 237, 0.1);
				transition: all 0.3s;

				&:active {
					transform: scale(0.95);
				}

				&.active {
					background: linear-gradient(135deg, #e4edff, #d0e0ff);
					box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
				}
			}

			.function-name {
				font-size: 26rpx;
				color: #666;
			}
		}
	}

	.more-settings {
		margin-top: 10rpx;
		border-top: 1px solid rgba(240, 240, 240, 0.6);
		padding-top: 20rpx;
		animation: slideDown 0.3s ease-out;
	}

	@keyframes slideDown {
		from {
			opacity: 0;
			transform: translateY(-10rpx);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* 兴趣图表样式 */
	.interest-chart {
		padding: 20rpx 0;

		.chart-container {
			display: flex;
			justify-content: center;
			margin-bottom: 30rpx;

			.ring-chart {
				position: relative;
				width: 260rpx;
				height: 260rpx;
				border-radius: 50%;

				.ring-segment {
					position: absolute;
					width: 100%;
					height: 100%;
					border-radius: 50%;
				}

				.ring-empty {
					width: 100%;
					height: 100%;
					border-radius: 50%;
					background: linear-gradient(135deg, #f5f5f5, #e8e8e8);
					display: flex;
					align-items: center;
					justify-content: center;

					.empty-text {
						font-size: 24rpx;
						color: #999;
					}
				}

				.ring-inner {
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					width: 150rpx;
					height: 150rpx;
					border-radius: 50%;
					background-color: #fff;
					display: flex;
					align-items: center;
					justify-content: center;
					box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.05);

					.ring-center-text {
						font-size: 32rpx;
						font-weight: bold;
						background: linear-gradient(to right, #3264ED, #5A8BFF);
						-webkit-background-clip: text;
						background-clip: text;
						color: transparent;
					}
				}
			}
		}

		.chart-legend {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-around;

			.legend-item {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;
				width: 45%;

				.legend-color {
					width: 24rpx;
					height: 24rpx;
					border-radius: 50%;
					margin-right: 10rpx;
					box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
					/* 动态颜色通过style属性设置 */
				}

				.legend-text {
					font-size: 24rpx;
					color: #666;
				}
			}
		}
	}

	/* 收藏列表样式 */
	.collection-list {
		display: flex;
		overflow-x: auto;
		scrollbar-width: none;

		/* Firefox */
		&::-webkit-scrollbar {
			display: none;
			/* Chrome, Safari */
		}

		.collection-item {
			width: 300rpx;
			margin-right: 30rpx;
			flex-shrink: 0;
			cursor: pointer;
			transition: transform 0.2s ease;

			&:active {
				transform: scale(0.98);
			}

			.collection-image {
				position: relative;
				width: 100%;
				height: 180rpx;
				border-radius: 16rpx;
				overflow: hidden;
				margin-bottom: 15rpx;
				box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

				image {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}

				.collection-overlay {
					position: absolute;
					bottom: 0;
					right: 0;
					background: rgba(0, 0, 0, 0.5);
					backdrop-filter: blur(5px);
					-webkit-backdrop-filter: blur(5px);
					padding: 4rpx 12rpx;
					border-radius: 12rpx 0 0 0;
					display: flex;
					align-items: center;

					text {
						color: #fff;
						font-size: 20rpx;
						margin-left: 4rpx;
					}
				}
			}

			.collection-title {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 10rpx;
				line-height: 1.4;
				height: 80rpx;
				overflow: hidden;
			}

			.collection-info {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.collection-tag {
					font-size: 22rpx;
					color: #3264ED;
					background-color: #f0f6ff;
					display: inline-block;
					padding: 4rpx 14rpx;
					border-radius: 20rpx;
				}

				.collection-time {
					font-size: 22rpx;
					color: #999;
				}
			}
		}
	}

	/* 分析列表样式 */
	.analysis-list {
		.analysis-item {
			display: flex;
			padding: 20rpx 0;
			border-bottom: 1px solid #f5f7fa;

			&:last-child {
				border-bottom: none;
				padding-bottom: 0;
			}

			.analysis-left {
				margin-right: 20rpx;

				.analysis-date {
					width: 70rpx;
					text-align: center;

					.date-day {
						display: block;
						font-size: 32rpx;
						font-weight: bold;
						color: #333;
					}

					.date-month {
						display: block;
						font-size: 22rpx;
						color: #999;
					}
				}
			}

			.analysis-content {
				flex: 1;

				.analysis-title {
					font-size: 30rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 10rpx;
				}

				.analysis-desc {
					font-size: 26rpx;
					color: #666;
					line-height: 1.5;
					margin-bottom: 12rpx;
				}

				.analysis-meta {
					display: flex;

					.meta-item {
						display: flex;
						align-items: center;
						margin-right: 20rpx;

						text {
							font-size: 22rpx;
							color: #999;
							margin-left: 4rpx;
						}
					}
				}
			}
		}
	}

	/* 空状态样式 */
	.empty-state {
		text-align: center;
		padding: 40rpx 20rpx;
		color: #999;
		font-size: 26rpx;
	}
</style>