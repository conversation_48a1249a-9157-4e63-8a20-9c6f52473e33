<template>
	<view class="demo-page">
		<view class="demo-header">
			<view class="back-btn" @click="goBack">
				<uni-icons type="left" size="20" color="#333"></uni-icons>
				<text>返回</text>
			</view>
			<view class="demo-title">AI小助手演示</view>
		</view>
		
		<view class="demo-content">
			<view class="demo-section">
				<view class="section-title">功能演示</view>
				<view class="demo-card">
					<view class="card-title">📱 浮窗小助手</view>
					<view class="card-desc">右下角的AI小助手浮窗，点击可查看智能分析结果</view>
				</view>
				
				<view class="demo-card">
					<view class="card-title">🤖 智能分析</view>
					<view class="card-desc">根据页面内容提供个性化的AI分析，包括内容摘要、核心观点等</view>
				</view>
				
				<view class="demo-card">
					<view class="card-title">🎨 美观界面</view>
					<view class="card-desc">采用现代化设计，支持动画效果和响应式布局</view>
				</view>
			</view>
			
			<view class="demo-section">
				<view class="section-title">测试数据</view>
				<view class="test-controls">
					<view class="control-btn" @click="switchToArticle">
						<text>切换到文章模式</text>
					</view>
					<view class="control-btn" @click="switchToHotDetail">
						<text>切换到热点模式</text>
					</view>
					<view class="control-btn" @click="toggleAssistant">
						<text>{{ showAssistant ? '隐藏' : '显示' }}小助手</text>
					</view>
				</view>
			</view>
			
			<view class="demo-section">
				<view class="section-title">当前配置</view>
				<view class="config-info">
					<view class="config-item">
						<text class="config-label">页面类型:</text>
						<text class="config-value">{{ currentPageType }}</text>
					</view>
					<view class="config-item">
						<text class="config-label">显示状态:</text>
						<text class="config-value">{{ showAssistant ? '显示' : '隐藏' }}</text>
					</view>
					<view class="config-item">
						<text class="config-label">数据标题:</text>
						<text class="config-value">{{ currentData.title || '无' }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- AI小助手 -->
		<AIAssistant 
			:pageType="currentPageType" 
			:contentData="currentData" 
			:visible="showAssistant"
		/>
	</view>
</template>

<script>
import AIAssistant from '../../components/AIAssistant.vue';

export default {
	components: {
		AIAssistant
	},
	data() {
		return {
			showAssistant: true,
			currentPageType: 'article',
			articleData: {
				title: '2024年AI技术发展趋势分析',
				description: '深度解析AI技术在各行业的应用前景，探讨人工智能未来发展方向',
				author: '智索编辑部',
				publishTime: new Date().toISOString(),
				viewCount: 2300,
				likeCount: 156,
				commentCount: 23
			},
			hotDetailData: {
				title: '人工智能技术突破引发行业关注',
				hotValue: 85,
				viewCount: 5600,
				searchCount: 1200,
				commentCount: 45,
				source: '科技日报',
				tags: ['人工智能', '技术突破', '行业发展']
			}
		}
	},
	computed: {
		currentData() {
			return this.currentPageType === 'article' ? this.articleData : this.hotDetailData;
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		
		switchToArticle() {
			this.currentPageType = 'article';
			uni.showToast({
				title: '已切换到文章模式',
				icon: 'success'
			});
		},
		
		switchToHotDetail() {
			this.currentPageType = 'hotDetail';
			uni.showToast({
				title: '已切换到热点模式',
				icon: 'success'
			});
		},
		
		toggleAssistant() {
			this.showAssistant = !this.showAssistant;
			uni.showToast({
				title: this.showAssistant ? '小助手已显示' : '小助手已隐藏',
				icon: 'success'
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.demo-page {
	min-height: 100vh;
	background-color: #f8f9fc;
}

.demo-header {
	display: flex;
	align-items: center;
	padding: 20rpx 30rpx;
	background-color: #fff;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	
	.back-btn {
		display: flex;
		align-items: center;
		margin-right: 30rpx;
		
		text {
			margin-left: 10rpx;
			font-size: 28rpx;
			color: #333;
		}
	}
	
	.demo-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
}

.demo-content {
	padding: 30rpx;
}

.demo-section {
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	padding-left: 20rpx;
	border-left: 6rpx solid #722ED1;
}

.demo-card {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	
	.card-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 16rpx;
	}
	
	.card-desc {
		font-size: 26rpx;
		color: #666;
		line-height: 1.6;
	}
}

.test-controls {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.control-btn {
	background-color: #722ED1;
	color: #fff;
	padding: 24rpx;
	border-radius: 12rpx;
	text-align: center;
	font-size: 28rpx;
	font-weight: 500;
	transition: all 0.3s ease;
	
	&:active {
		background-color: #5a1ea6;
		transform: scale(0.98);
	}
}

.config-info {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.config-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
}

.config-label {
	font-size: 26rpx;
	color: #666;
}

.config-value {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}
</style>
