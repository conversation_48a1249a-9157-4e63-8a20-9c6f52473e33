package com.zhisuo.app.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * AI分析请求DTO
 */
@Data
public class AIAnalysisRequest {
    
    /**
     * 页面类型 (article, hotDetail等)
     */
    @NotBlank(message = "页面类型不能为空")
    private String pageType;
    
    /**
     * 内容数据
     */
    private ContentData contentData;
    
    /**
     * 内容数据
     */
    @Data
    public static class ContentData {
        /**
         * 标题
         */
        private String title;
        
        /**
         * 描述
         */
        private String description;
        
        /**
         * 作者
         */
        private String author;
        
        /**
         * 发布时间
         */
        private String publishTime;
        
        /**
         * 浏览量
         */
        private Integer viewCount;
        
        /**
         * 点赞数
         */
        private Integer likeCount;
        
        /**
         * 评论数
         */
        private Integer commentCount;
        
        /**
         * 内容摘要
         */
        private String summary;
        
        /**
         * 标签
         */
        private List<String> tags;
    }
}
