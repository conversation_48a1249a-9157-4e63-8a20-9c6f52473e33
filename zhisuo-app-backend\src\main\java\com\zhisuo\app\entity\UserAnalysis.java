package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 用户分析报告实体类
 */
@Data
@TableName("user_analysis")
public class UserAnalysis {
    
    @TableId
    private String analysisId;    // 分析ID
    
    private String userId;        // 用户ID
    
    private String title;         // 分析标题
    
    private String description;   // 分析描述
    
    private String content;       // 分析内容
    
    private Integer hotValue;     // 热度值
    
    private Integer commentCount; // 评论数
    
    private Date createTime;      // 创建时间
    
    private Date updateTime;      // 更新时间
    
    private Integer status;       // 状态(1:显示,0:隐藏)
} 