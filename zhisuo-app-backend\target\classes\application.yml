server:
  port: 8080
  servlet:
    context-path: / # 修改为根路径，便于前端访问

spring:
  application:
    name: zhisuo-app
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************
    username: root
    password: root
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: gcf021206
    database: 0
    timeout: 10000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
  # MVC配置
  mvc:
    # CORS配置
    cors:
      mapping: /**
      allowed-origins: '*'
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: '*'
      allow-credentials: true
      max-age: 3600
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  # HTTP编码配置
  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  # Jackson配置
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.zhisuo.app.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_id
      table-underline: true

# JWT配置
jwt:
  secret: 1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef
  expiration: 86400 # 24小时(单位:秒)
  issuer: zhisuo-app

# 自定义应用配置
app:
  # 短信验证码配置
  sms:
    expire-time: 60 # 验证码有效期(单位:秒)
    default-code: "6666" # 默认验证码固定为6666(仅开发环境使用)

# 央视新闻配置
cctv:
  news:
    url: https://news.cctv.com/2019/07/gaiban/cmsdatainterface/page/news_1.jsonp
    max-news: 20 # 获取前N条热点新闻
    timeout: 10000 # 请求超时时间(毫秒)
    delay: 1000 # 请求间隔时间(毫秒)

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: oss-cn-guangzhou.aliyuncs.com
    access-key-id: LTAI5tPzwQpzrzu6w5kBzfSm
    access-key-secret: ******************************
    bucket-name: fcg02

# 硅基流动AI配置
siliconflow:
  ai:
    base-url: https://api.siliconflow.cn/v1
    api-key: sk-cdfyayyhykqrxprjsibsacmufczvfyrmmnewomtcrcmcduin
    model: Qwen/Qwen3-8B
    max-tokens: 512
    temperature: 0.7
    top-p: 0.7
    timeout: 30000 # 请求超时时间(毫秒)