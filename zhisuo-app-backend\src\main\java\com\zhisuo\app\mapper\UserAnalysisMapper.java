package com.zhisuo.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhisuo.app.entity.UserAnalysis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

/**
 * 用户分析Mapper接口
 */
@Mapper
public interface UserAnalysisMapper extends BaseMapper<UserAnalysis> {
    
    /**
     * 获取用户分析列表（分页）
     *
     * @param page 分页对象
     * @param userId 用户ID
     * @return 用户分析列表
     */
    @Select("SELECT analysis_id, title, description, hot_value, comment_count, create_time " +
            "FROM user_analysis WHERE user_id = #{userId} AND status = 1 " +
            "ORDER BY create_time DESC")
    Page<Map<String, Object>> selectUserAnalysis(Page<Map<String, Object>> page, @Param("userId") String userId);
    
    /**
     * 统计用户分析数量
     *
     * @param userId 用户ID
     * @return 分析数量
     */
    @Select("SELECT COUNT(*) FROM user_analysis WHERE user_id = #{userId} AND status = 1")
    long countByUserId(@Param("userId") String userId);
}
