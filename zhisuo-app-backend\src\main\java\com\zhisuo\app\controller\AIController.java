package com.zhisuo.app.controller;

import com.zhisuo.app.common.Result;
import com.zhisuo.app.dto.request.AIAnalysisRequest;
import com.zhisuo.app.dto.request.AIChatRequest;
import com.zhisuo.app.dto.response.AIAnalysisResponse;
import com.zhisuo.app.dto.response.AIChatResponse;
import com.zhisuo.app.service.AIService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * AI功能控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/ai")
public class AIController {
    
    @Autowired
    private AIService aiService;
    
    /**
     * AI对话
     */
    @PostMapping("/chat")
    public Result<AIChatResponse> chat(@Valid @RequestBody AIChatRequest request) {
        log.info("AI对话请求: {}", request.getMessage());
        
        try {
            AIChatResponse response = aiService.chat(request);
            log.info("AI对话响应: {}", response.getReply());
            return Result.success(response);
        } catch (Exception e) {
            log.error("AI对话异常: {}", e.getMessage(), e);
            return Result.error("AI对话服务异常: " + e.getMessage());
        }
    }
    
    /**
     * AI内容分析
     */
    @PostMapping("/analyze")
    public Result<AIAnalysisResponse> analyze(@Valid @RequestBody AIAnalysisRequest request) {
        log.info("AI分析请求: pageType={}, title={}", 
                request.getPageType(), 
                request.getContentData() != null ? request.getContentData().getTitle() : "无");
        
        try {
            AIAnalysisResponse response = aiService.analyze(request);
            log.info("AI分析响应: summary={}", response.getSummary());
            return Result.success(response);
        } catch (Exception e) {
            log.error("AI分析异常: {}", e.getMessage(), e);
            return Result.error("AI分析服务异常: " + e.getMessage());
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("AI服务运行正常");
    }
}
