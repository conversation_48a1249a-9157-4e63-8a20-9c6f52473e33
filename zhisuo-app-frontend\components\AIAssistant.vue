<template>
	<view class="ai-assistant-container" v-if="showAssistant">
		<!-- 浮窗小助手按钮 -->
		<view
			class="ai-float-button"
			:class="{
				'active': showPanel,
				'dragging': isDragging,
				'long-pressing': isLongPressing && !isDragging
			}"
			:style="{ right: buttonPosition.right, bottom: buttonPosition.bottom }"
			@touchstart.stop="onTouchStart"
			@touchmove.stop.prevent="onTouchMove"
			@touchend.stop="onTouchEnd"
			@touchcancel.stop="onTouchCancel"
			@click.stop="onButtonClick"
		>
			<image src="/static/logo.png" class="ai-logo" mode="aspectFit"></image>
			<view class="ai-pulse" v-if="!showPanel && !isDragging && !isLongPressing"></view>
		</view>

		<!-- 聊天面板 -->
		<view class="ai-chat-panel" v-if="showPanel" @click.stop="">
			<!-- 面板头部 -->
			<view class="ai-chat-header">
				<view class="ai-header-left">
					<image src="/static/logo.png" class="ai-header-logo" mode="aspectFit"></image>
					<text class="ai-title">AI智能助手</text>
				</view>
				<view class="ai-header-right">
					<view class="ai-header-btn" @click="minimizePanel">
						<uni-icons type="minus" size="16" color="#666"></uni-icons>
					</view>
					<view class="ai-header-btn" @click="closePanel">
						<uni-icons type="close" size="16" color="#666"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 标签页 -->
			<view class="ai-tabs" v-if="showAnalysis">
				<view class="ai-tab" :class="{ 'active': activeTab === 'chat' }" @click="switchTab('chat')">
					<uni-icons type="chatboxes" size="16" :color="activeTab === 'chat' ? '#B37FEB' : '#5f6368'"></uni-icons>
					<text>聊天</text>
				</view>
				<view class="ai-tab" :class="{ 'active': activeTab === 'analysis' }" @click="switchTab('analysis')">
					<uni-icons type="bars" size="16" :color="activeTab === 'analysis' ? '#B37FEB' : '#5f6368'"></uni-icons>
					<text>分析</text>
				</view>
			</view>

			<!-- 聊天内容区域 -->
			<view class="ai-chat-content" v-if="!showAnalysis || activeTab === 'chat'">
				<scroll-view class="chat-messages" scroll-y="true" :scroll-top="scrollTop" scroll-with-animation="true">
					<!-- 消息列表 -->
					<view v-for="(message, index) in messages" :key="index" class="message-wrapper">
						<!-- AI消息 -->
						<view v-if="message.type === 'ai'" class="message-item ai-message">
							<view class="avatar-container">
								<view class="ai-avatar">
									<image src="/static/logo.png" mode="aspectFit"></image>
								</view>
							</view>
							<view class="message-content">
								<view class="message-bubble ai-bubble">
									<text class="message-text">{{ formatMessageContent(message.content) }}</text>
								</view>
							</view>
						</view>

						<!-- 用户消息 -->
						<view v-else-if="message.type === 'user'" class="message-item user-message">
							<view class="message-content">
								<view class="message-bubble user-bubble">
									<text class="message-text">{{ formatMessageContent(message.content) }}</text>
								</view>
							</view>
							<view class="avatar-container">
								<view class="user-avatar">
									<image v-if="userInfo && userInfo.avatar" :src="userInfo.avatar" mode="aspectFill"></image>
									<text v-else class="user-avatar-text">我</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 加载状态 -->
					<view v-if="loading" class="message-wrapper">
						<view class="message-item ai-message">
							<view class="avatar-container">
								<view class="ai-avatar">
									<image src="/static/logo.png" mode="aspectFit"></image>
								</view>
							</view>
							<view class="message-content">
								<view class="message-bubble ai-bubble typing-bubble">
									<view class="typing-indicator">
										<view class="typing-dot"></view>
										<view class="typing-dot"></view>
										<view class="typing-dot"></view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>

			<!-- 分析内容区域 -->
			<view class="ai-analysis-content" v-if="showAnalysis && activeTab === 'analysis'">
				<!-- 分析工具栏 -->
				<view class="ai-analysis-toolbar">
					<view class="ai-analysis-title">智能分析报告</view>
					<view class="ai-refresh-btn" @click="refreshAnalysis" :class="{ 'loading': analysisLoading }">
						<uni-icons type="refresh" size="16" :color="analysisLoading ? '#ccc' : '#722ED1'"></uni-icons>
						<text>{{ analysisLoading ? '生成中...' : '重新分析' }}</text>
					</view>
				</view>

				<scroll-view class="ai-analysis-scroll" scroll-y="true">
					<view v-if="analysisResult && !analysisLoading" class="ai-analysis-result">
						<view class="ai-section" v-for="(section, index) in analysisResult" :key="index">
							<view class="ai-section-title">{{ section.title }}</view>
							<view class="ai-section-content">{{ section.content }}</view>
						</view>
					</view>
					<view v-else-if="analysisLoading" class="ai-analysis-loading">
						<view class="ai-loading-spinner"></view>
						<text>AI正在生成智能分析，请稍候...</text>
					</view>
					<view v-else class="ai-analysis-empty">
						<text>点击"重新分析"按钮生成智能分析报告</text>
					</view>
				</scroll-view>
			</view>

			<!-- 输入区域 -->
			<view class="ai-input-area" v-if="activeTab === 'chat'">
				<view class="ai-input-container">
					<input
						class="ai-input"
						v-model="inputText"
						placeholder="请输入您的问题..."
						@confirm="sendMessage"
						:disabled="loading"
					/>
					<view class="ai-send-btn" @click="sendMessage" :class="{ 'disabled': !inputText.trim() || loading }">
						<uni-icons type="paperplane-filled" size="20" color="#fff"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<!-- 遮罩层 -->
		<view class="ai-mask" v-if="showPanel" @click="closePanel"></view>
	</view>
</template>

<script>
import { callAIChat, formatMessageHistory, generateAnalysis, generateFallbackReply } from '../common/api/aiService.js';

export default {
	name: 'AIAssistant',
	props: {
		// 页面类型，用于确定分析内容
		pageType: {
			type: String,
			default: 'article' // article | hotDetail
		},
		// 内容数据
		contentData: {
			type: Object,
			default: () => ({})
		},
		// 是否显示助手
		visible: {
			type: Boolean,
			default: true
		},
		// 是否显示分析功能，默认为true（显示），设为false时只显示聊天功能
		showAnalysis: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			showAssistant: true,
			showPanel: false, // 默认显示悬浮窗，不显示面板
			loading: false,
			analysisLoading: false,
			analysisResult: null,
			activeTab: 'chat',
			inputText: '',
			messages: [],
			scrollTop: 0,
			showUserAvatar: true, // 控制是否显示用户头像
			userInfo: null, // 用户信息
			userInfoTimer: null, // 用户信息检查定时器
			// 拖拽相关数据
			isDragging: false,
			isLongPressing: false, // 是否正在长按
			longPressTimer: null, // 长按定时器
			startX: 0, // 触摸起始X坐标
			startY: 0, // 触摸起始Y坐标
			dragStartRight: 30, // 拖拽开始时的右边距
			dragStartBottom: 200, // 拖拽开始时的底边距
			currentX: 30, // 当前右边距
			currentY: 200, // 当前底边距
			touchIdentifier: null, // 触摸标识符
			buttonPosition: {
				right: '30rpx',
				bottom: '200rpx'
			}
		}
	},
	computed: {
		hasValidData() {
			return this.contentData &&
				   typeof this.contentData === 'object' &&
				   Object.keys(this.contentData).length > 0;
		},
		dataTitle() {
			return this.hasValidData ? (this.contentData.title || '未知标题') : '加载中...';
		}
	},
	watch: {
		visible(newVal) {
			this.showAssistant = newVal;
		},
		contentData: {
			handler(newVal) {
				if (newVal && Object.keys(newVal).length > 0) {
					this.generateMockAnalysis();
				}
			},
			deep: true,
			immediate: true
		}
	},
	mounted() {
		// 延迟执行，确保数据已经传入
		this.$nextTick(() => {
			this.initializeChat();
			this.loadButtonPosition();
			this.loadUserInfo();
		});

		// 添加全局触摸事件监听，防止拖拽时触摸丢失
		document.addEventListener('touchmove', this.handleGlobalTouchMove, { passive: false });
		document.addEventListener('touchend', this.handleGlobalTouchEnd);
		document.addEventListener('touchcancel', this.handleGlobalTouchCancel);

		// 监听页面可见性变化（H5兼容方式）
		if (typeof document !== 'undefined') {
			document.addEventListener('visibilitychange', () => {
				if (!document.hidden) {
					this.refreshUserInfo();
				}
			});
		}
	},

	beforeUnmount() {
		// 清理全局事件监听
		document.removeEventListener('touchmove', this.handleGlobalTouchMove);
		document.removeEventListener('touchend', this.handleGlobalTouchEnd);
		document.removeEventListener('touchcancel', this.handleGlobalTouchCancel);

		// 清理定时器
		this.clearLongPressTimer();

		// 清理用户信息监听定时器
		if (this.userInfoTimer) {
			clearInterval(this.userInfoTimer);
		}
	},
	methods: {
		// 加载用户信息
		loadUserInfo() {
			// 从本地存储获取用户信息
			this.userInfo = uni.getStorageSync('userInfo');
			//console.log('AI助手加载用户信息:', this.userInfo);

			// 启动定时器检查用户信息变化
			this.startUserInfoWatcher();
		},

		// 启动用户信息监听器
		startUserInfoWatcher() {
			// 清除之前的定时器
			if (this.userInfoTimer) {
				clearInterval(this.userInfoTimer);
			}

			// 每5秒检查一次用户信息是否有变化
			this.userInfoTimer = setInterval(() => {
				const newUserInfo = uni.getStorageSync('userInfo');
				const oldAvatar = this.userInfo?.avatar;
				const newAvatar = newUserInfo?.avatar;

				// 只有当头像发生变化时才更新
				if (oldAvatar !== newAvatar) {
					this.userInfo = newUserInfo;
					//console.log('AI助手检测到用户头像变化:', newAvatar);
				}
			}, 5000);
		},

		// 刷新用户信息（供外部调用）
		refreshUserInfo() {
			this.userInfo = uni.getStorageSync('userInfo');
			//console.log('AI助手用户信息已刷新:', this.userInfo);
		},

		// 格式化消息内容 - 去除多余的空白和换行
		formatMessageContent(content) {
			if (!content) return '';

			// 去除开头和结尾的空白字符
			let formatted = content.trim();

			// 去除开头的多余换行符
			formatted = formatted.replace(/^\n+/, '');

			// 去除结尾的多余换行符
			formatted = formatted.replace(/\n+$/, '');

			// 将多个连续的换行符替换为最多两个换行符（段落分隔）
			formatted = formatted.replace(/\n{3,}/g, '\n\n');

			// 去除行首行尾的空格，但保留换行
			formatted = formatted.replace(/[ \t]+$/gm, '').replace(/^[ \t]+/gm, '');

			return formatted;
		},

		togglePanel() {
			this.showPanel = !this.showPanel;
			if (this.showPanel && this.messages.length === 0) {
				this.initializeChat();
			}
		},

		// 触摸开始
		onTouchStart(e) {
			// 确保有触摸点
			if (!e.touches || e.touches.length === 0) {
				return;
			}

			// 先清理之前的状态
			this.clearLongPressTimer();

			const touch = e.touches[0];
			this.isDragging = false;
			this.isLongPressing = false;
			this.startX = touch.clientX;
			this.startY = touch.clientY;

			// 记录当前位置作为拖拽起始位置
			this.dragStartRight = this.currentX;
			this.dragStartBottom = this.currentY;

			// 记录触摸标识符，用于跟踪同一个触摸点
			this.touchIdentifier = touch.identifier;

			// 记录开始触摸的时间
			this.touchStartTime = Date.now();

			// 启动长按定时器（500ms后进入拖拽模式）
			this.longPressTimer = setTimeout(() => {
				// 只有在还在触摸状态下才设置长按
				if (this.touchIdentifier !== null && !this.isDragging) {
					this.isLongPressing = true;
				}
			}, 500);
		},

		// 触摸移动
		onTouchMove(e) {
			// 确保有触摸点
			if (!e.touches || e.touches.length === 0) {
				return;
			}

			// 找到对应的触摸点
			let touch = null;
			for (let i = 0; i < e.touches.length; i++) {
				if (e.touches[i].identifier === this.touchIdentifier) {
					touch = e.touches[i];
					break;
				}
			}

			// 如果找不到对应的触摸点，使用第一个
			if (!touch) {
				touch = e.touches[0];
			}

			const deltaX = touch.clientX - this.startX;
			const deltaY = touch.clientY - this.startY;
			const moveDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

			// 如果还没有长按且移动距离过大，取消长按定时器（防止意外触发）
			if (!this.isLongPressing && !this.isDragging && moveDistance > 50) {
				this.clearLongPressTimer();
				return;
			}

			// 检查是否应该开始拖拽
			const currentTime = Date.now();
			const touchDuration = currentTime - this.touchStartTime;

			// 如果触摸时间超过500ms且移动距离足够，直接开始拖拽
			if (touchDuration >= 500 && !this.isDragging && moveDistance > 3) {
				// 确保设置长按状态
				if (!this.isLongPressing) {
					this.isLongPressing = true;
					this.clearLongPressTimer();
				}
				this.isDragging = true;
			}
			// 或者已经是长按状态且移动距离足够
			else if (this.isLongPressing && !this.isDragging && moveDistance > 3) {
				this.isDragging = true;
			}

			if (this.isDragging) {
				// 获取屏幕尺寸
				const systemInfo = uni.getSystemInfoSync();
				const screenWidth = systemInfo.screenWidth;
				const screenHeight = systemInfo.screenHeight;

				// 计算新位置（转换为rpx）
				const buttonSize = 75; // 按钮大小 75rpx
				const margin = 15; // 边距

				// 将像素偏移转换为rpx偏移
				const deltaRightRpx = deltaX * (750 / screenWidth);
				const deltaBottomRpx = deltaY * (750 / screenWidth);

				// 计算新位置：基于拖拽开始时的位置 + 偏移量
				// deltaX为正表示向右移动，right应该减小（因为right是距离右边的距离）
				// deltaY为正表示向下移动，bottom应该减小（因为bottom是距离底部的距离）
				let newRight = this.dragStartRight - deltaRightRpx;
				let newBottom = this.dragStartBottom - deltaBottomRpx;

				// 动态获取屏幕高度对应的rpx值
				const screenHeightRpx = screenHeight * 750 / screenWidth;

				// 边界限制
				const maxRight = 750 - buttonSize - margin;
				const maxBottom = screenHeightRpx - buttonSize - margin;

				newRight = Math.max(margin, Math.min(newRight, maxRight));
				newBottom = Math.max(margin, Math.min(newBottom, maxBottom));

				// 实时更新位置
				this.buttonPosition.right = newRight + 'rpx';
				this.buttonPosition.bottom = newBottom + 'rpx';

				// 实时更新当前位置（用于边界检查）
				this.currentX = newRight;
				this.currentY = newBottom;
			}
		},

		// 触摸结束
		onTouchEnd(e) {
			this.clearLongPressTimer();

			if (this.isDragging) {
				// 如果是拖拽，完成拖拽操作
				this.finishDrag();
			} else {
				// 如果不是拖拽，判断是否为点击
				const touchDuration = Date.now() - this.touchStartTime;
				if (touchDuration < 500 && !this.isLongPressing) {
					// 短时间触摸且未长按，触发点击
					this.handleClick();
				}
				this.resetTouchState();
			}
		},

		// 触摸取消（当触摸被系统中断时）
		onTouchCancel(e) {
			this.clearLongPressTimer();
			if (this.isDragging) {
				this.finishDrag();
			} else {
				this.resetTouchState();
			}
		},

		// 完成拖拽的通用方法
		finishDrag() {
			if (this.isDragging) {
				// currentX和currentY在拖拽过程中已经实时更新，这里直接保存即可
				// 持久化保存位置
				this.saveButtonPosition();
			}
			this.resetTouchState();
		},

		// 清除长按定时器
		clearLongPressTimer() {
			if (this.longPressTimer) {
				clearTimeout(this.longPressTimer);
				this.longPressTimer = null;
			}
		},

		// 重置触摸状态
		resetTouchState() {
			this.isDragging = false;
			this.isLongPressing = false;
			this.touchIdentifier = null;
			this.clearLongPressTimer();
		},

		// 处理点击事件
		handleClick() {
			this.togglePanel();
		},



		// 按钮点击事件（备用，主要通过触摸事件处理）
		onButtonClick(e) {
			// 阻止默认点击行为，因为我们通过触摸事件处理
			e.preventDefault();
		},

		// 加载按钮位置
		loadButtonPosition() {
			try {
				const savedPosition = uni.getStorageSync('ai_assistant_position');
				if (savedPosition) {
					const position = JSON.parse(savedPosition);
					this.currentX = position.right || 30;
					this.currentY = position.bottom || 200;
					this.buttonPosition.right = this.currentX + 'rpx';
					this.buttonPosition.bottom = this.currentY + 'rpx';
				}
			} catch (error) {
				console.log('加载AI助手位置失败:', error);
				// 设置默认位置
				this.currentX = 30;
				this.currentY = 200;
				this.buttonPosition.right = this.currentX + 'rpx';
				this.buttonPosition.bottom = this.currentY + 'rpx';
			}
		},

		// 保存按钮位置
		saveButtonPosition() {
			try {
				const position = {
					right: this.currentX,
					bottom: this.currentY
				};
				uni.setStorageSync('ai_assistant_position', JSON.stringify(position));
			} catch (error) {
				console.log('保存AI助手位置失败:', error);
			}
		},

		// 全局触摸移动处理
		handleGlobalTouchMove(e) {
			if (this.isDragging) {
				e.preventDefault();
				this.onTouchMove(e);
			}
		},

		// 全局触摸结束处理
		handleGlobalTouchEnd(e) {
			if (this.isDragging) {
				this.finishDrag();
			}
		},

		// 全局触摸取消处理
		handleGlobalTouchCancel(e) {
			if (this.isDragging) {
				this.finishDrag();
			}
		},

		closePanel() {
			// 关闭面板，回到悬浮窗状态
			this.showPanel = false;
		},

		minimizePanel() {
			// 最小化面板，回到悬浮窗状态
			this.showPanel = false;
		},

		switchTab(tab) {
			// 如果不显示分析功能，则不允许切换到分析标签
			if (!this.showAnalysis && tab === 'analysis') {
				return;
			}
			this.activeTab = tab;
			if (tab === 'analysis' && !this.analysisResult && !this.analysisLoading) {
				this.generateMockAnalysis();
			}
		},

		async refreshAnalysis() {
			if (this.analysisLoading) return;

			this.analysisLoading = true;
			this.analysisResult = null;

			try {
				await this.generateMockAnalysis();
				uni.showToast({
					title: '分析已更新',
					icon: 'success'
				});
			} catch (error) {
				console.error('刷新分析失败:', error);
				uni.showToast({
					title: '分析生成失败',
					icon: 'error'
				});
			} finally {
				this.analysisLoading = false;
			}
		},

		initializeChat() {
			if (this.messages.length === 0) {
				// 添加欢迎消息
				this.messages.push({
					type: 'ai',
					content: '您好！我是智索AI助手 🤖\n\n我可以帮您：\n• 回答各种问题\n• 分析内容和数据\n• 提供专业建议\n• 协助解决问题\n\n请随时告诉我您需要什么帮助！',
					timestamp: new Date()
				});
				this.scrollToBottom();
			}
		},

		async sendMessage() {
			if (!this.inputText.trim() || this.loading) return;

			const userMessage = this.inputText.trim();
			this.inputText = '';

			// 添加用户消息
			this.messages.push({
				type: 'user',
				content: userMessage,
				timestamp: new Date(),
				isUser: true
			});

			this.scrollToBottom();
			this.loading = true;

			try {
				// 调用后端AI接口
				const messageHistory = formatMessageHistory(this.messages, 10);
				// 聊天模式不传递上下文信息，分析模式才传递
				const pageType = this.activeTab === 'chat' ? null : this.pageType;
				const contentData = this.activeTab === 'chat' ? null : this.contentData;
				const aiReply = await callAIChat(userMessage, pageType, contentData, messageHistory);

				this.messages.push({
					type: 'ai',
					content: aiReply,
					timestamp: new Date(),
					isUser: false
				});
			} catch (error) {
				console.error('AI回复失败:', error);
				// 如果API调用失败，使用备用回复
				this.messages.push({
					type: 'ai',
					content: generateFallbackReply(userMessage),
					timestamp: new Date(),
					isUser: false
				});
			}

			this.loading = false;
			this.scrollToBottom();
		},



		scrollToBottom() {
			this.$nextTick(() => {
				this.scrollTop = 999999;
			});
		},
		
		async generateMockAnalysis() {
			// 检查数据是否有效
			if (!this.contentData || Object.keys(this.contentData).length === 0) {
				this.analysisResult = this.getDefaultAnalysis();
				return;
			}

			// 如果不是通过refreshAnalysis调用，设置loading状态
			if (!this.analysisLoading) {
				this.analysisLoading = true;
			}

			try {
				// 使用后端AI生成智能分析
				const analysisResult = await generateAnalysis(this.pageType, this.contentData);
				this.analysisResult = analysisResult;
			} catch (error) {
				console.error('生成AI分析失败:', error);
				// 如果AI分析失败，使用备用分析
				if (this.pageType === 'article') {
					this.analysisResult = this.getArticleAnalysis();
				} else if (this.pageType === 'hotDetail') {
					this.analysisResult = this.getHotDetailAnalysis();
				} else {
					this.analysisResult = this.getDefaultAnalysis();
				}
			} finally {
				// 只有在不是通过refreshAnalysis调用时才重置loading状态
				if (this.analysisLoading && !this.refreshing) {
					this.analysisLoading = false;
				}
			}
		},

		parseAnalysisText(analysisText) {
			// 尝试解析AI返回的分析文本，将其转换为结构化数据
			const sections = [];
			const lines = analysisText.split('\n');
			let currentSection = null;

			for (const line of lines) {
				const trimmedLine = line.trim();
				if (!trimmedLine) continue;

				// 检查是否是标题行（包含数字序号或特殊符号）
				if (trimmedLine.match(/^[0-9]+\.|^[•·▪▫◦‣⁃]\s|^#{1,3}\s|^【.*】|^[📊🎯💡🔍📈🔥👥💭⏳]/)) {
					if (currentSection) {
						sections.push(currentSection);
					}
					currentSection = {
						title: trimmedLine.replace(/^[0-9]+\.\s*/, '').replace(/^[•·▪▫◦‣⁃]\s*/, ''),
						content: ''
					};
				} else if (currentSection) {
					// 添加到当前段落的内容
					if (currentSection.content) {
						currentSection.content += '\n' + trimmedLine;
					} else {
						currentSection.content = trimmedLine;
					}
				} else {
					// 如果没有当前段落，创建一个默认段落
					sections.push({
						title: '📋 分析结果',
						content: trimmedLine
					});
				}
			}

			// 添加最后一个段落
			if (currentSection) {
				sections.push(currentSection);
			}

			// 如果没有解析出任何段落，返回原文本
			if (sections.length === 0) {
				sections.push({
					title: '🤖 AI分析',
					content: analysisText
				});
			}

			return sections;
		},
		
		getArticleAnalysis() {
			// 安全地获取数据，提供默认值
			const title = (this.contentData && this.contentData.title) || '当前文章';
			const viewCount = (this.contentData && this.contentData.viewCount) || 0;
			const likeCount = (this.contentData && this.contentData.likeCount) || 0;

			return [
				{
					title: '📊 内容摘要',
					content: `${title}主要讨论了相关领域的最新发展趋势，涵盖了技术创新、市场动态和未来展望等多个维度。`
				},
				{
					title: '🎯 核心观点',
					content: '1. 技术发展呈现加速趋势\n2. 市场需求持续增长\n3. 创新应用不断涌现\n4. 行业竞争日趋激烈'
				},
				{
					title: '💡 深度洞察',
					content: '基于当前趋势分析，该领域在未来2-3年内将迎来重要发展机遇期，建议关注相关技术突破和政策导向。'
				},
				{
					title: '🔍 相关建议',
					content: '建议深入了解相关技术细节，关注行业动态，把握投资和发展机会。'
				},
				{
					title: '📈 数据洞察',
					content: `文章阅读量：${viewCount}，点赞数：${likeCount}，显示出${viewCount > 1000 ? '较高' : '一般'}的关注度。`
				}
			];
		},
		
		getHotDetailAnalysis() {
			// 安全地获取数据，提供默认值
			const title = (this.contentData && this.contentData.title) || '当前热点';
			const hotValue = (this.contentData && this.contentData.hotValue) || 0;
			const viewCount = (this.contentData && this.contentData.viewCount) || 0;
			const commentCount = (this.contentData && this.contentData.commentCount) || 0;

			return [
				{
					title: '🔥 热度分析',
					content: `${title}当前热度值为${hotValue}，在同类话题中处于${hotValue > 80 ? '高热度' : hotValue > 50 ? '中等热度' : '一般热度'}水平。`
				},
				{
					title: '📈 趋势预测',
					content: '基于数据模型分析，该话题热度预计在未来24-48小时内保持稳定，可能出现小幅波动。'
				},
				{
					title: '👥 用户画像',
					content: '关注该话题的用户主要集中在25-40岁年龄段，具有较高的教育背景和专业素养。'
				},
				{
					title: '🎯 传播特征',
					content: '该话题具有较强的社交传播属性，主要通过专业社群和知识分享平台进行扩散。'
				},
				{
					title: '💭 影响评估',
					content: '预计该话题将对相关行业产生中等程度影响，建议持续关注后续发展。'
				},
				{
					title: '📊 数据统计',
					content: `浏览量：${viewCount}，评论数：${commentCount}，互动率：${viewCount > 0 ? ((commentCount / viewCount) * 100).toFixed(2) : 0}%`
				}
			];
		},
		
		getDefaultAnalysis() {
			return [
				{
					title: '🤖 AI助手',
					content: 'AI智能分析助手已就绪，正在为您提供个性化的内容分析服务。'
				},
				{
					title: '📋 功能说明',
					content: '• 智能内容摘要\n• 核心观点提取\n• 趋势分析预测\n• 个性化建议'
				},
				{
					title: '💡 使用提示',
					content: '页面内容加载完成后，AI将自动为您生成详细的分析报告。点击"重新分析"按钮可获取最新结果。'
				},
				{
					title: '⏳ 状态说明',
					content: this.contentData && Object.keys(this.contentData).length > 0 ?
						'内容数据已加载，分析功能正常' :
						'等待页面内容加载中...'
				}
			];
		}
	}
}
</script>

<style lang="scss" scoped>
.ai-assistant-container {
	position: fixed;
	z-index: 9999;
}

/* 浮窗按钮 */
.ai-float-button {
	position: fixed;
	width: 75rpx;
	height: 75rpx;
	background: linear-gradient(135deg, #722ED1, #B37FEB);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(66, 133, 244, 0.3);
	transition: all 0.3s ease;
	z-index: 10000;
	cursor: move;
	touch-action: none; /* 禁用默认触摸行为 */
	user-select: none; /* 禁用文本选择 */

	&:active {
		transform: scale(0.95);
	}

	&.active {
		background: linear-gradient(135deg, #722ED1, #B37FEB);
		transform: scale(0.1);
		opacity: 0.3;
		animation: shrinkToCenter 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

		.ai-pulse {
			display: none;
		}
	}

	&.long-pressing {
		transform: scale(1.05);
		box-shadow: 0 12rpx 36rpx rgba(114, 46, 209, 0.4);
		background: linear-gradient(135deg, #8B5CF6, #C084FC);
		animation: longPressGlow 0.3s ease-in-out;

		.ai-pulse {
			display: none;
		}

		.ai-logo {
			transform: scale(0.95);
			animation: logoGlow 0.3s ease-in-out;
		}
	}

	&.dragging {
		transition: none !important;
		transform: scale(1.15);
		box-shadow: 0 20rpx 50rpx rgba(114, 46, 209, 0.6);
		z-index: 10001;
		opacity: 0.95;
		filter: brightness(1.1);
		background: linear-gradient(135deg, #8B5CF6, #C084FC);

		.ai-pulse {
			display: none;
		}

		.ai-logo {
			transform: scale(0.85);
			filter: brightness(1.2);
		}
	}
}

.ai-logo {
	width: 70rpx;
	height: 70rpx;
	border-radius: 50%;
}

/* 脉冲动画 */
.ai-pulse {
	position: absolute;
	top: -10rpx;
	left: -10rpx;
	right: -10rpx;
	bottom: -10rpx;
	border: 4rpx solid rgba(181, 76, 238, 0.4);
	border-radius: 50%;
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	100% {
		transform: scale(1.3);
		opacity: 0;
	}
}

/* 遮罩层 */
.ai-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.3);
	z-index: 9998;
	animation: fadeIn 0.3s ease-out;
}

/* 聊天面板 */
.ai-chat-panel {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 700rpx;
	height: 800rpx;
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
	z-index: 9999;
	animation: expandFromFloat 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	display: flex;
	flex-direction: column;
	overflow: hidden;
	box-sizing: border-box;
}

@keyframes expandFromFloat {
	0% {
		transform: translate(-50%, -50%) scale(0.1);
		opacity: 0;
		border-radius: 50%;
	}
	30% {
		transform: translate(-50%, -50%) scale(0.3);
		opacity: 0.6;
		border-radius: 40rpx;
	}
	70% {
		transform: translate(-50%, -50%) scale(0.9);
		opacity: 0.9;
		border-radius: 20rpx;
	}
	100% {
		transform: translate(-50%, -50%) scale(1);
		opacity: 1;
		border-radius: 16rpx;
	}
}

@keyframes shrinkToCenter {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	100% {
		transform: scale(0.1);
		opacity: 0.3;
	}
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

/* 面板头部 */
.ai-chat-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 24rpx;
	border-bottom: 1rpx solid #e8eaed;
	background-color: #f8f9fa;
	border-radius: 16rpx 16rpx 0 0;
}

.ai-header-left {
	display: flex;
	align-items: center;
}

.ai-header-logo {
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	margin-right: 12rpx;
}

.ai-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #202124;
}

.ai-header-right {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.ai-header-btn {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	transition: all 0.2s ease;

	&:hover {
		background-color: #f1f3f4;
	}

	&:active {
		background-color: #e8eaed;
		transform: scale(0.95);
	}
}

/* 标签页 */
.ai-tabs {
	display: flex;
	border-bottom: 1rpx solid #e8eaed;
	background-color: #fff;
}

.ai-tab {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 24rpx 16rpx;
	font-size: 26rpx;
	color: #5f6368;
	cursor: pointer;
	transition: all 0.2s ease;
	border-bottom: 4rpx solid transparent;

	text {
		margin-left: 8rpx;
		transition: color 0.2s ease;
	}

	/* 图标过渡效果 */
	:deep(.uni-icons) {
		transition: color 0.2s ease;
	}

	&.active {
		color: #B37FEB;
		border-bottom-color: #722ED1;
		background-color: #f8f9ff;
	}

	&:active {
		background-color: #f1f3f4;
	}
}

/* 聊天内容区域 */
.ai-chat-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	height: calc(100vh - 200rpx);
}

/* 聊天消息样式 - 现代化设计 */
.chat-messages {
	flex: 1;
	padding: 16rpx 0;
	background-color: #f5f5f5;
	overflow-y: auto;
	min-height: 0;
}

.message-wrapper {
	margin-bottom: 24rpx;
}

.message-item {
	display: flex;
	align-items: flex-start;
	width: 100%;
	padding: 0 32rpx;
}

.ai-message {
	justify-content: flex-start;
}

.user-message {
	justify-content: flex-end;
}

.avatar-container {
	flex-shrink: 0;
}

.message-content {
	max-width: calc(100% - 140rpx);
	word-wrap: break-word;
}

.ai-message .message-content {
	margin-left: 0;
	margin-right: 32rpx;
}

.user-message .message-content {
	margin-left: 32rpx;
	margin-right: 32rpx;
	text-align: right;
}

.ai-avatar {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 8rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);

	image {
		width: 40rpx;
		height: 40rpx;
	}
}

.user-avatar {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #B37FEB 0%, #9C5CE8 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 8rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(179, 127, 235, 0.2);

	image {
		width: 64rpx;
		height: 64rpx;
		border-radius: 50%;
	}
}

.user-avatar-text {
	color: white;
	font-size: 24rpx;
	font-weight: 600;
}

.message-bubble {
	padding: 20rpx 24rpx;
	border-radius: 20rpx;
	word-wrap: break-word;
	word-break: break-word;
	position: relative;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
	display: inline-block;
	max-width: 100%;
	margin-top: 8rpx;
}

.ai-bubble {
	background-color: #ffffff;
	border: 1rpx solid #e8e8e8;
	color: #333333;
	border-bottom-left-radius: 8rpx;
}

.user-bubble {
	background: linear-gradient(135deg, #B37FEB 0%, #9C5CE8 100%);
	color: white;
	border-bottom-right-radius: 8rpx;
	margin-left: auto;
	display: inline-block;
}

.message-text {
	font-size: 28rpx;
	line-height: 1.6;
	letter-spacing: 0.5rpx;
	white-space: pre-wrap;
	word-wrap: break-word;
	word-break: break-word;
}

/* 打字动画 - 现代化设计 */
.typing-bubble {
	padding: 20rpx 28rpx !important;
	background-color: #ffffff !important;
	border: 1rpx solid #e8e8e8 !important;
}

.typing-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 40rpx;
}

.typing-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background-color: #B37FEB;
	margin: 0 6rpx;
	animation: typing-bounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
	animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
	animation-delay: -0.16s;
}

.typing-dot:nth-child(3) {
	animation-delay: 0s;
}

@keyframes typing-bounce {
	0%, 80%, 100% {
		transform: scale(0.8);
		opacity: 0.5;
	}
	40% {
		transform: scale(1.2);
		opacity: 1;
	}
}

/* 输入区域 */
.ai-input-area {
	padding: 20rpx;
	border-top: 1rpx solid #e8eaed;
	background-color: #fff;
	border-radius: 0 0 16rpx 16rpx;
}

.ai-input-container {
	display: flex;
	align-items: center;
	background-color: #f8f9fa;
	border-radius: 48rpx;
	padding: 8rpx 8rpx 8rpx 24rpx;
	border: 2rpx solid transparent;
	transition: all 0.2s ease;

	&:focus-within {
		border-color: #B37FEB;
		background-color: #fff;
	}
}

.ai-input {
	flex: 1;
	border: none;
	outline: none;
	background: transparent;
	font-size: 26rpx;
	color: #202124;
	padding: 16rpx 0;

	&::placeholder {
		color: #9aa0a6;
	}
}

.ai-send-btn {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	background-color: #722ED1;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 16rpx;
	transition: all 0.2s ease;

	&:active {
		transform: scale(0.95);
	}

	&.disabled {
		background-color: #e8eaed;
		opacity: 0.5;
	}
}

/* 分析内容区域 */
.ai-analysis-content {
	flex: 1;
	overflow: hidden;
	min-height: 0;
	display: flex;
	flex-direction: column;
}

/* 分析工具栏 */
.ai-analysis-toolbar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 24rpx;
	border-bottom: 1rpx solid #e8eaed;
	background-color: #f8f9fa;
}

.ai-analysis-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #202124;
}

.ai-refresh-btn {
	display: flex;
	align-items: center;
	padding: 12rpx 20rpx;
	background-color: #fff;
	border: 1rpx solid #722ED1;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #722ED1;
	transition: all 0.2s ease;

	text {
		margin-left: 8rpx;
	}

	&:active {
		background-color: #f0f0f0;
		transform: scale(0.95);
	}

	&.loading {
		color: #ccc;
		border-color: #ccc;
		pointer-events: none;

		:deep(.uni-icons) {
			animation: spin 1s linear infinite;
		}
	}
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.ai-analysis-scroll {
	flex: 1;
	overflow-y: auto;
	padding: 20rpx;
	box-sizing: border-box;
}

.ai-analysis-result {
	width: 100%;
	box-sizing: border-box;

	.ai-section {
		margin-bottom: 30rpx;
		width: 100%;
		box-sizing: border-box;

		&:last-child {
			margin-bottom: 0;
		}
	}
}

.ai-section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #722ED1;
	margin-bottom: 16rpx;
	display: flex;
	align-items: center;
	width: 100%;
	box-sizing: border-box;
	overflow: hidden;
}

.ai-section-content {
	font-size: 26rpx;
	color: #5f6368;
	line-height: 1.6;
	white-space: pre-wrap;
	word-wrap: break-word;
	word-break: break-word;
	background-color: #f8f9fa;
	padding: 20rpx;
	border-radius: 12rpx;
	border-left: 6rpx solid #722ED1;
	overflow: hidden;
	box-sizing: border-box;
	max-width: 100%;
}

.ai-analysis-empty {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 200rpx;

	text {
		font-size: 28rpx;
		color: #9aa0a6;
	}
}

.ai-analysis-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 300rpx;

	text {
		font-size: 26rpx;
		color: #722ED1;
		margin-top: 20rpx;
	}
}

.ai-loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 6rpx solid #f3f3f3;
	border-top: 6rpx solid #722ED1;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

/* 长按反馈动画 */
@keyframes longPressGlow {
	0% {
		box-shadow: 0 8rpx 24rpx rgba(114, 46, 209, 0.3);
	}
	50% {
		box-shadow: 0 16rpx 48rpx rgba(114, 46, 209, 0.6);
		transform: scale(1.08);
	}
	100% {
		box-shadow: 0 12rpx 36rpx rgba(114, 46, 209, 0.4);
		transform: scale(1.05);
	}
}

@keyframes logoGlow {
	0% {
		filter: brightness(1);
	}
	50% {
		filter: brightness(1.3);
		transform: scale(0.9);
	}
	100% {
		filter: brightness(1.1);
		transform: scale(0.95);
	}
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
	.ai-chat-panel {
		width: 90%;
		height: 80%;
		max-width: 600rpx;
		max-height: 800rpx;
	}

	.ai-float-button {
		width: 100rpx;
		height: 100rpx;
	}

	.ai-logo {
		width: 50rpx;
		height: 50rpx;
	}

	.ai-message-content {
		max-width: 400rpx;
	}
}
</style>
