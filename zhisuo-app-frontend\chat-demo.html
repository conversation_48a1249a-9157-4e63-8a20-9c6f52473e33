<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手对话样式演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .chat-container {
            max-width: 400px;
            margin: 0 auto;
            height: 100vh;
            background: white;
            display: flex;
            flex-direction: column;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .chat-header {
            background: linear-gradient(135deg, #B37FEB 0%, #9C5CE8 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-weight: 600;
            font-size: 18px;
        }

        .chat-messages {
            flex: 1;
            padding: 16px 20px;
            background-color: #f5f5f5;
            overflow-y: auto;
        }

        .message-wrapper {
            margin-bottom: 24px;
        }

        .message-item {
            display: flex;
            align-items: flex-start;
            width: 100%;
            padding: 0 32px;
        }

        .ai-message {
            justify-content: flex-start;
        }

        .user-message {
            justify-content: flex-end;
        }

        .avatar-container {
            flex-shrink: 0;
        }

        .message-content {
            max-width: calc(100% - 140px);
            word-wrap: break-word;
        }

        .ai-message .message-content {
            margin-left: 0;
            margin-right: 32px;
        }

        .user-message .message-content {
            margin-left: 32px;
            margin-right: 32px;
            text-align: right;
        }

        .ai-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 4px;
            overflow: hidden;
            box-shadow: 0 1px 4px rgba(0, 122, 255, 0.2);
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #B37FEB 0%, #9C5CE8 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 4px;
            overflow: hidden;
            box-shadow: 0 1px 4px rgba(179, 127, 235, 0.2);
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .message-bubble {
            padding: 10px 12px;
            border-radius: 10px;
            word-wrap: break-word;
            word-break: break-word;
            position: relative;
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
            display: inline-block;
            max-width: 100%;
            margin-top: 4px;
        }

        .ai-bubble {
            background-color: #ffffff;
            border: 1px solid #e8e8e8;
            color: #333333;
            border-bottom-left-radius: 4px;
        }

        .user-bubble {
            background: linear-gradient(135deg, #B37FEB 0%, #9C5CE8 100%);
            color: white;
            border-bottom-right-radius: 4px;
            margin-left: auto;
            display: inline-block;
        }

        .message-text {
            font-size: 15px;
            line-height: 1.6;
            letter-spacing: 0.25px;
        }

        /* 打字动画 */
        .typing-bubble {
            padding: 10px 14px !important;
            background-color: #ffffff !important;
            border: 1px solid #e8e8e8 !important;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 20px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #B37FEB;
            margin: 0 3px;
            animation: typing-bounce 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) {
            animation-delay: -0.32s;
        }

        .typing-dot:nth-child(2) {
            animation-delay: -0.16s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0s;
        }

        @keyframes typing-bounce {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        .demo-controls {
            padding: 20px;
            background: white;
            border-top: 1px solid #e8e8e8;
            text-align: center;
        }

        .demo-btn {
            background: linear-gradient(135deg, #B37FEB 0%, #9C5CE8 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            margin: 0 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .demo-btn:hover {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            AI智能助手 - 新版对话样式
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <!-- AI消息 -->
            <div class="message-wrapper">
                <div class="message-item ai-message">
                    <div class="avatar-container">
                        <div class="ai-avatar">AI</div>
                    </div>
                    <div class="message-content">
                        <div class="message-bubble ai-bubble">
                            <div class="message-text">您好！我是智索AI助手 😊<br><br>我可以帮您：<br>• 回答各种问题<br>• 分析内容和数据<br>• 提供专业建议<br>• 协助解决问题<br><br>请随时告诉我您需要什么帮助！</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户消息 -->
            <div class="message-wrapper">
                <div class="message-item user-message">
                    <div class="message-content">
                        <div class="message-bubble user-bubble">
                            <div class="message-text">你好！这个新的对话界面看起来很不错，头像和对话框水平排列的布局更符合常见的聊天应用设计。</div>
                        </div>
                    </div>
                    <div class="avatar-container">
                        <div class="user-avatar">我</div>
                    </div>
                </div>
            </div>

            <!-- AI回复 -->
            <div class="message-wrapper">
                <div class="message-item ai-message">
                    <div class="avatar-container">
                        <div class="ai-avatar">AI</div>
                    </div>
                    <div class="message-content">
                        <div class="message-bubble ai-bubble">
                            <div class="message-text">谢谢您的反馈！新的对话界面确实采用了更现代化的设计：<br><br>✨ <strong>主要改进：</strong><br>• 头像和对话框水平排列，符合用户习惯<br>• AI头像在左侧，用户头像在右侧<br>• 对话框向下对齐，视觉更自然<br>• 优化的间距和排版<br>• 流畅的打字动画效果<br><br>这样的设计更符合微信、QQ等主流聊天应用的布局，让用户感觉更熟悉！</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 打字动画演示 -->
            <div class="message-wrapper" id="typingDemo" style="display: none;">
                <div class="message-item ai-message">
                    <div class="avatar-container">
                        <div class="ai-avatar">AI</div>
                    </div>
                    <div class="message-content">
                        <div class="message-bubble ai-bubble typing-bubble">
                            <div class="typing-indicator">
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-controls">
            <button class="demo-btn" onclick="showTyping()">显示打字动画</button>
            <button class="demo-btn" onclick="addMessage()">添加消息</button>
            <button class="demo-btn" onclick="scrollToBottom()">滚动到底部</button>
        </div>
    </div>

    <script>
        function showTyping() {
            const typingDemo = document.getElementById('typingDemo');
            typingDemo.style.display = 'block';
            scrollToBottom();
            
            setTimeout(() => {
                typingDemo.style.display = 'none';
                addAIMessage('这就是新的打字动画效果！是不是很流畅？');
            }, 3000);
        }

        function addMessage() {
            addUserMessage('这个界面真的很棒！');
            setTimeout(() => {
                addAIMessage('感谢您的认可！我们会继续优化用户体验。');
            }, 1000);
        }

        function addUserMessage(text) {
            const chatMessages = document.getElementById('chatMessages');
            const messageWrapper = document.createElement('div');
            messageWrapper.className = 'message-wrapper';
            messageWrapper.innerHTML = `
                <div class="message-item user-message">
                    <div class="message-header">
                        <div class="user-avatar">我</div>
                    </div>
                    <div class="message-content">
                        <div class="message-bubble user-bubble">
                            <div class="message-text">${text}</div>
                        </div>
                    </div>
                </div>
            `;
            chatMessages.appendChild(messageWrapper);
            scrollToBottom();
        }

        function addAIMessage(text) {
            const chatMessages = document.getElementById('chatMessages');
            const messageWrapper = document.createElement('div');
            messageWrapper.className = 'message-wrapper';
            messageWrapper.innerHTML = `
                <div class="message-item ai-message">
                    <div class="message-header">
                        <div class="ai-avatar">AI</div>
                    </div>
                    <div class="message-content">
                        <div class="message-bubble ai-bubble">
                            <div class="message-text">${text}</div>
                        </div>
                    </div>
                </div>
            `;
            chatMessages.appendChild(messageWrapper);
            scrollToBottom();
        }

        function scrollToBottom() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    </script>
</body>
</html>
