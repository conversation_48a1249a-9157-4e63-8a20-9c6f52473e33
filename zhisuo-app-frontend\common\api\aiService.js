/**
 * AI服务 - 调用后端AI接口
 */

// 后端API配置
const API_CONFIG = {
	baseURL: 'http://localhost:8080/api',
	timeout: 30000
};

/**
 * 调用后端AI聊天接口
 * @param {string} message - 用户消息
 * @param {string} pageType - 页面类型
 * @param {Object} contentData - 内容数据
 * @param {Array} messageHistory - 消息历史
 * @returns {Promise<string>} AI回复内容
 */
export async function callAIChat(message, pageType = '', contentData = null, messageHistory = []) {
	const url = `${API_CONFIG.baseURL}/ai/chat`;

	const requestBody = {
		message: message,
		pageType: pageType,
		contentData: contentData,
		messageHistory: messageHistory
	};

	try {
		const response = await fetch(url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(requestBody)
		});

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}

		const result = await response.json();

		if (result.code === 0 && result.data) {
			return result.data.reply;
		} else {
			throw new Error(result.message || 'AI服务响应错误');
		}
	} catch (error) {
		console.error('AI聊天接口调用失败:', error);
		throw error;
	}
}

/**
 * 调用后端AI分析接口
 * @param {string} pageType - 页面类型
 * @param {Object} contentData - 内容数据
 * @returns {Promise<Object>} AI分析结果
 */
export async function generateAnalysis(pageType, contentData) {
	const url = `${API_CONFIG.baseURL}/ai/analyze`;

	const requestBody = {
		pageType: pageType,
		contentData: contentData
	};

	try {
		const response = await fetch(url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(requestBody)
		});

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}

		const result = await response.json();

		if (result.code === 0 && result.data) {
			return result.data;
		} else {
			throw new Error(result.message || 'AI分析服务响应错误');
		}
	} catch (error) {
		console.error('AI分析接口调用失败:', error);
		throw error;
	}
}

/**
 * 格式化消息历史
 * @param {Array} messages - 消息数组
 * @param {number} maxCount - 最大保留消息数
 * @returns {Array} 格式化后的消息历史
 */
export function formatMessageHistory(messages, maxCount = 10) {
	if (!messages || messages.length === 0) {
		return [];
	}

	// 只保留最近的消息
	const recentMessages = messages.slice(-maxCount);

	return recentMessages.map(msg => ({
		role: msg.isUser ? 'user' : 'assistant',
		content: msg.content,
		timestamp: msg.timestamp
	}));
}

/**
 * 生成备用回复
 * @param {string} userMessage - 用户消息
 * @returns {string} 备用回复
 */
export function generateFallbackReply(userMessage) {
	const message = userMessage.toLowerCase();

	if (message.includes('你好') || message.includes('hello')) {
		return '你好！我是智索AI小助手，很高兴为您服务！有什么可以帮助您的吗？';
	} else if (message.includes('功能') || message.includes('能做什么')) {
		return '我可以帮您分析内容、回答问题、提供建议等。虽然现在网络有些不稳定，但我会尽力为您提供帮助！';
	} else if (message.includes('分析')) {
		return '我可以为您分析当前内容的关键信息、情感倾向和相关话题。请稍后重试分析功能。';
	} else {
		return '抱歉，我现在遇到了一些技术问题，无法正常回复。请稍后重试，或者尝试重新描述您的问题。';
	}
}

/**
 * 生成备用分析结果
 * @returns {Object} 备用分析结果
 */
export function generateFallbackAnalysis() {
	return {
		summary: '暂时无法生成智能分析，请稍后重试',
		keywords: ['智索', '内容'],
		sentiment: '中性',
		hotScore: 5,
		recommendation: '值得关注的内容',
		relatedTopics: ['热门话题'],
		isFallback: true
	};
}