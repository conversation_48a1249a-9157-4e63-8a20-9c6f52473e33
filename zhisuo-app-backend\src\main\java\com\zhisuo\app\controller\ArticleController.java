package com.zhisuo.app.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhisuo.app.common.Result;
import com.zhisuo.app.common.context.UserContext;

import com.zhisuo.app.dto.response.ArticleWithTagsResponse;
import com.zhisuo.app.entity.Article;
import com.zhisuo.app.entity.Tag;
import com.zhisuo.app.service.ArticleService;
import com.zhisuo.app.service.ContentTagService;

import com.zhisuo.app.service.LikeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 文章控制器
 */
@RestController
@RequestMapping("/v1/articles")
public class ArticleController {
    
    @Autowired
    private ArticleService articleService;

    @Autowired
    private LikeService likeService;



    @Autowired
    private ContentTagService contentTagService;
    
    /**
     * 获取文章分页列表
     *
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param source 来源筛选
     * @param sortBy 排序字段
     * @param sortOrder 排序方向
     * @return 文章分页列表
     */
    @GetMapping
    public Result<Map<String, Object>> getArticles(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String source,
            @RequestParam(defaultValue = "publishTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortOrder) {

        // 创建分页对象，注意page+1是因为前端从0开始，后端从1开始
        Page<Article> pageRequest = new Page<>(page + 1, size);
        Page<Article> pageResult = articleService.getArticlePage(pageRequest, source, sortBy, sortOrder);

        // 为每个文章添加标签信息
        List<ArticleWithTagsResponse> contentWithTags = pageResult.getRecords().stream()
                .map(article -> {
                    // 获取标签
                    List<Tag> tags = contentTagService.getContentTags(article.getArticleId(), "article");
                    return ArticleWithTagsResponse.fromEntityWithTags(article, tags);
                })
                .collect(Collectors.toList());

        // 构建符合接口文档的返回结构
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("content", contentWithTags);
        resultMap.put("totalElements", pageResult.getTotal());
        resultMap.put("totalPages", pageResult.getPages());
        resultMap.put("size", pageResult.getSize());
        resultMap.put("number", page);

        return Result.success(resultMap);
    }
    
    /**
     * 获取今日文章分页列表
     *
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param source 来源筛选
     * @return 今日文章分页列表
     */
    @GetMapping("/today")
    public Result<Map<String, Object>> getTodayArticles(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String source) {
        
        // 创建分页对象，注意page+1是因为前端从0开始，后端从1开始
        Page<Article> pageRequest = new Page<>(page + 1, size);
        Page<Article> pageResult = articleService.getTodayArticlePage(pageRequest, source);

        // 为每个文章添加标签信息
        List<ArticleWithTagsResponse> contentWithTags = pageResult.getRecords().stream()
                .map(article -> {
                    // 获取标签
                    List<Tag> tags = contentTagService.getContentTags(article.getArticleId(), "article");
                    return ArticleWithTagsResponse.fromEntityWithTags(article, tags);
                })
                .collect(Collectors.toList());

        // 构建符合接口文档的返回结构
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("content", contentWithTags);
        resultMap.put("totalElements", pageResult.getTotal());
        resultMap.put("totalPages", pageResult.getPages());
        resultMap.put("size", pageResult.getSize());
        resultMap.put("number", page);

        return Result.success(resultMap);
    }
    
    /**
     * 获取文章详情
     *
     * @param articleId 文章ID
     * @return 文章详情
     */
    @GetMapping("/{articleId}")
    public Result<ArticleWithTagsResponse> getArticleDetail(@PathVariable String articleId) {
        Article article = articleService.getById(articleId);
        if (article == null) {
            return Result.error(404, "文章不存在");
        }

        // 获取标签信息
        List<Tag> tags = contentTagService.getContentTags(articleId, "article");
        ArticleWithTagsResponse response = ArticleWithTagsResponse.fromEntityWithTags(article, tags);

        return Result.success(response);
    }
    
    /**
     * 更新文章的浏览量
     *
     * @param articleId 文章ID
     * @return 结果
     */
    @PostMapping("/{articleId}/view")
    public Result<Void> updateViewCount(@PathVariable String articleId) {
        // 更新浏览量
        articleService.updateViewCount(articleId);
        return Result.success();
    }
    
    /**
     * 切换文章点赞状态
     *
     * @param articleId 文章ID
     * @return 点赞状态
     */
    @PostMapping("/{articleId}/like")
    public Result<Map<String, Object>> toggleLike(@PathVariable String articleId) {
        String userId = UserContext.getUserId();

        boolean isLiked = likeService.toggleLike(articleId, "article", userId);

        Map<String, Object> result = new HashMap<>();
        result.put("isLiked", isLiked);
        result.put("message", isLiked ? "点赞成功" : "取消点赞成功");

        return Result.success(result);
    }

    /**
     * 更新文章的点赞量（兼容旧接口）
     *
     * @param articleId 文章ID
     * @return 结果
     */
    @PostMapping("/{articleId}/like-count")
    public Result<Void> updateLikeCount(@PathVariable String articleId) {
        // 更新点赞量
        articleService.updateLikeCount(articleId);
        return Result.success();
    }
    

    
    /**
     * 根据来源获取文章列表
     *
     * @param source 来源
     * @param limit 数量限制
     * @return 文章列表
     */
    @GetMapping("/by-source")
    public Result<List<ArticleWithTagsResponse>> getArticlesBySource(
            @RequestParam String source,
            @RequestParam(defaultValue = "10") Integer limit) {

        List<Article> articles = articleService.getArticlesBySource(source, limit);

        // 为每个文章添加标签信息
        List<ArticleWithTagsResponse> articlesWithTags = articles.stream()
                .map(article -> {
                    // 获取标签
                    List<Tag> tags = contentTagService.getContentTags(article.getArticleId(), "article");
                    return ArticleWithTagsResponse.fromEntityWithTags(article, tags);
                })
                .collect(Collectors.toList());

        return Result.success(articlesWithTags);
    }
    
    /**
     * 手动触发央视新闻更新
     *
     * @return 结果
     */
    @PostMapping("/refresh-cctv")
    public Result<Void> refreshCCTVNews() {
        articleService.fetchAndUpdateCCTVNews();
        return Result.success();
    }
}
