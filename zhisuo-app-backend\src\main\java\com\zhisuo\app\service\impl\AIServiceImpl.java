package com.zhisuo.app.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zhisuo.app.config.AIConfig;
import com.zhisuo.app.dto.request.AIAnalysisRequest;
import com.zhisuo.app.dto.request.AIChatRequest;
import com.zhisuo.app.dto.response.AIAnalysisResponse;
import com.zhisuo.app.dto.response.AIChatResponse;
import com.zhisuo.app.service.AIService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * AI服务实现类
 */
@Slf4j
@Service
public class AIServiceImpl implements AIService {
    
    @Autowired
    private AIConfig aiConfig;
    
    @Override
    public AIChatResponse chat(AIChatRequest request) {
        try {
            log.info("开始处理AI对话请求: {}", request.getMessage());

            // 检查配置
            if (aiConfig == null) {
                log.error("AI配置为空");
                return AIChatResponse.fallback(generateFallbackReply(request.getMessage()));
            }

            log.info("AI配置: baseUrl={}, model={}", aiConfig.getBaseUrl(), aiConfig.getModel());

            // 根据是否有上下文信息决定使用哪种提示词
            String systemPrompt;
            if (request.getPageType() != null && request.getContentData() != null) {
                // 有上下文信息，使用分析提示词
                systemPrompt = buildAnalysisSystemPrompt(request.getPageType(), request.getContentData());
                log.info("使用分析模式系统提示词");
            } else {
                // 无上下文信息，使用纯聊天提示词
                systemPrompt = buildChatSystemPrompt();
                log.info("使用聊天模式系统提示词");
            }
            log.info("系统提示词: {}", systemPrompt);

            // 构建消息列表
            JSONArray messages = new JSONArray();

            // 添加系统消息
            JSONObject systemMessage = new JSONObject();
            systemMessage.put("role", "system");
            systemMessage.put("content", systemPrompt);
            messages.add(systemMessage);

            // 添加历史消息 (最多保留10条)
            if (request.getMessageHistory() != null && !request.getMessageHistory().isEmpty()) {
                List<AIChatRequest.ChatMessage> history = request.getMessageHistory();
                int startIndex = Math.max(0, history.size() - 10);
                for (int i = startIndex; i < history.size(); i++) {
                    AIChatRequest.ChatMessage msg = history.get(i);
                    JSONObject historyMessage = new JSONObject();
                    historyMessage.put("role", msg.getRole());
                    historyMessage.put("content", msg.getContent());
                    messages.add(historyMessage);
                }
            }

            // 添加当前用户消息
            JSONObject userMessage = new JSONObject();
            userMessage.put("role", "user");
            userMessage.put("content", request.getMessage());
            messages.add(userMessage);

            log.info("构建的消息数组: {}", messages.toString());

            // 调用硅基流动API
            String reply = callSiliconFlowAPI(messages);

            // 构建响应
            String messageId = UUID.randomUUID().toString();
            return new AIChatResponse(reply, messageId);

        } catch (Exception e) {
            log.error("AI对话失败: {}", e.getMessage(), e);
            // 返回备用回复
            return AIChatResponse.fallback(generateFallbackReply(request.getMessage()));
        }
    }
    
    @Override
    public AIAnalysisResponse analyze(AIAnalysisRequest request) {
        try {
            // 构建分析提示词
            String analysisPrompt = buildAnalysisPrompt(request.getPageType(), request.getContentData());
            
            // 构建消息列表
            JSONArray messages = new JSONArray();
            JSONObject systemMessage = new JSONObject();
            systemMessage.put("role", "system");
            systemMessage.put("content", "你是一个专业的内容分析师，请根据用户提供的内容进行深度分析。");
            messages.add(systemMessage);
            
            JSONObject userMessage = new JSONObject();
            userMessage.put("role", "user");
            userMessage.put("content", analysisPrompt);
            messages.add(userMessage);
            
            // 调用硅基流动API
            String analysisResult = callSiliconFlowAPI(messages);
            
            // 解析分析结果
            return parseAnalysisResult(analysisResult);
            
        } catch (Exception e) {
            log.error("AI分析失败", e);
            // 返回备用分析
            return AIAnalysisResponse.fallback();
        }
    }
    
    /**
     * 调用硅基流动API
     */
    private String callSiliconFlowAPI(JSONArray messages) {
        try {
            String url = aiConfig.getBaseUrl() + "/chat/completions";
            log.info("调用硅基流动API: {}", url);

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("model", aiConfig.getModel());
            requestBody.put("max_tokens", aiConfig.getMaxTokens());
            requestBody.put("enable_thinking", true);
            requestBody.put("thinking_budget", 4096);
            requestBody.put("min_p", 0.05);
            requestBody.put("temperature", aiConfig.getTemperature());
            requestBody.put("top_p", aiConfig.getTopP());
            requestBody.put("top_k", 50);
            requestBody.put("frequency_penalty", 0.5);
            requestBody.put("n", 1);
            requestBody.put("stream", false);
            requestBody.put("messages", messages);

            log.info("请求体: {}", requestBody.toString());

            // 发送请求
            HttpResponse response = HttpRequest.post(url)
                    .header("Authorization", "Bearer " + aiConfig.getApiKey())
                    .header("Content-Type", "application/json")
                    .body(requestBody.toString())
                    .timeout(aiConfig.getTimeout() != null ? aiConfig.getTimeout() : 60000)
                    .execute();

            log.info("API响应状态: {}", response.getStatus());
            log.info("API响应内容: {}", response.body());

            if (!response.isOk()) {
                String errorMsg = "API调用失败: " + response.getStatus() + " " + response.body();
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }

            // 解析响应
            JSONObject responseJson = JSONUtil.parseObj(response.body());
            JSONArray choices = responseJson.getJSONArray("choices");
            if (choices == null || choices.isEmpty()) {
                log.error("API响应格式错误，没有choices字段");
                throw new RuntimeException("API响应格式错误");
            }

            JSONObject firstChoice = choices.getJSONObject(0);
            JSONObject message = firstChoice.getJSONObject("message");
            String content = message.getStr("content");

            log.info("AI回复内容: {}", content);
            return content;

        } catch (Exception e) {
            log.error("调用硅基流动API异常: {}", e.getMessage(), e);
            throw new RuntimeException("调用硅基流动API失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建聊天系统提示词 - 纯聊天，不包含上下文
     */
    private String buildChatSystemPrompt() {
        return "你是智索APP的AI小助手，一个专业、友好、有帮助的智能助手。请为用户提供有价值的帮助和建议，回答要简洁明了，语气友好自然。";
    }

    /**
     * 构建分析系统提示词 - 包含上下文信息
     */
    private String buildAnalysisSystemPrompt(String pageType, AIChatRequest.ContentData contentData) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是智索APP的AI小助手，一个专业、友好、有帮助的智能助手。");

        if ("article".equals(pageType) && contentData != null) {
            prompt.append("当前用户正在浏览文章：《").append(contentData.getTitle()).append("》");
            if (StrUtil.isNotBlank(contentData.getDescription())) {
                prompt.append("，文章描述：").append(contentData.getDescription());
            }
        } else if ("hotDetail".equals(pageType) && contentData != null) {
            prompt.append("当前用户正在浏览热点：《").append(contentData.getTitle()).append("》");
            if (StrUtil.isNotBlank(contentData.getDescription())) {
                prompt.append("，热点描述：").append(contentData.getDescription());
            }
        }

        prompt.append("请根据上下文为用户提供有价值的帮助和建议。回答要简洁明了，语气友好自然。");

        return prompt.toString();
    }
    
    /**
     * 构建分析提示词
     */
    private String buildAnalysisPrompt(String pageType, AIAnalysisRequest.ContentData contentData) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请对以下内容进行深度分析：\n\n");
        
        if (contentData != null) {
            prompt.append("标题：").append(contentData.getTitle()).append("\n");
            if (StrUtil.isNotBlank(contentData.getDescription())) {
                prompt.append("描述：").append(contentData.getDescription()).append("\n");
            }
            if (StrUtil.isNotBlank(contentData.getAuthor())) {
                prompt.append("作者：").append(contentData.getAuthor()).append("\n");
            }
            if (contentData.getViewCount() != null) {
                prompt.append("浏览量：").append(contentData.getViewCount()).append("\n");
            }
            if (contentData.getLikeCount() != null) {
                prompt.append("点赞数：").append(contentData.getLikeCount()).append("\n");
            }
        }
        
        prompt.append("\n请从以下几个维度进行分析：\n");
        prompt.append("1. 内容摘要（50字以内）\n");
        prompt.append("2. 关键词（3-5个，用逗号分隔）\n");
        prompt.append("3. 情感倾向（正面/中性/负面）\n");
        prompt.append("4. 热度评分（1-10分）\n");
        prompt.append("5. 推荐理由（30字以内）\n");
        prompt.append("6. 相关话题（2-3个，用逗号分隔）\n");
        prompt.append("\n请按照以上格式逐项回答，每项占一行。");
        
        return prompt.toString();
    }
    
    /**
     * 解析分析结果
     */
    private AIAnalysisResponse parseAnalysisResult(String analysisText) {
        AIAnalysisResponse response = new AIAnalysisResponse();
        
        try {
            String[] lines = analysisText.split("\n");
            for (String line : lines) {
                line = line.trim();
                if (line.contains("内容摘要") || line.contains("摘要")) {
                    response.setSummary(extractContent(line));
                } else if (line.contains("关键词")) {
                    String keywords = extractContent(line);
                    response.setKeywords(Arrays.asList(keywords.split("[,，]")));
                } else if (line.contains("情感") || line.contains("倾向")) {
                    response.setSentiment(extractContent(line));
                } else if (line.contains("热度") || line.contains("评分")) {
                    String scoreStr = extractContent(line).replaceAll("[^0-9]", "");
                    if (StrUtil.isNotBlank(scoreStr)) {
                        response.setHotScore(Integer.parseInt(scoreStr));
                    }
                } else if (line.contains("推荐") || line.contains("理由")) {
                    response.setRecommendation(extractContent(line));
                } else if (line.contains("相关话题") || line.contains("话题")) {
                    String topics = extractContent(line);
                    response.setRelatedTopics(Arrays.asList(topics.split("[,，]")));
                }
            }
        } catch (Exception e) {
            log.warn("解析分析结果失败", e);
        }
        
        // 设置默认值
        if (response.getSummary() == null) {
            response.setSummary("AI正在学习中，暂时无法生成摘要");
        }
        if (response.getKeywords() == null) {
            response.setKeywords(new ArrayList<>());
        }
        if (response.getSentiment() == null) {
            response.setSentiment("中性");
        }
        if (response.getHotScore() == null) {
            response.setHotScore(5);
        }
        if (response.getRecommendation() == null) {
            response.setRecommendation("值得关注的内容");
        }
        if (response.getRelatedTopics() == null) {
            response.setRelatedTopics(new ArrayList<>());
        }
        
        return response;
    }
    
    /**
     * 提取内容（去除标签和序号）
     */
    private String extractContent(String line) {
        // 去除数字序号和冒号
        line = line.replaceAll("^\\d+[.、]\\s*", "");
        // 去除标签部分
        if (line.contains("：")) {
            line = line.substring(line.indexOf("：") + 1);
        } else if (line.contains(":")) {
            line = line.substring(line.indexOf(":") + 1);
        }
        return line.trim();
    }
    
    /**
     * 生成备用回复
     */
    private String generateFallbackReply(String userMessage) {
        String message = userMessage.toLowerCase();
        
        if (message.contains("你好") || message.contains("hello")) {
            return "你好！我是智索AI小助手，很高兴为您服务！有什么可以帮助您的吗？";
        } else if (message.contains("功能") || message.contains("能做什么")) {
            return "我可以帮您分析内容、回答问题、提供建议等。虽然现在网络有些不稳定，但我会尽力为您提供帮助！";
        } else if (message.contains("分析")) {
            return "我可以为您分析当前内容的关键信息、情感倾向和相关话题。请稍后重试分析功能。";
        } else {
            return "抱歉，我现在遇到了一些技术问题，无法正常回复。请稍后重试，或者尝试重新描述您的问题。";
        }
    }
}
